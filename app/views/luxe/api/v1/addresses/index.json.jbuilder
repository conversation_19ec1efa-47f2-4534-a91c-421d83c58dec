json.addresses @addresses.each do |address|
  json.id address.id
  json.name address.name
  json.street_address address.street_address
  json.landmark address.landmark
  json.city address.city
  json.state address.state
  json.country address.country
  json.country_id Country.find_by_namei(address.country).try(:id)
  json.pincode address.pincode
  json.phone address.phone
  json.user_id address.user_id
  json.default address.default
  json.first_name address.first_name
  json.last_name address.last_name
end