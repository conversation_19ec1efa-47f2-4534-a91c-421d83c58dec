line_item_disc_message = cart.line_item_bmgnx_discount_message
json.line_items cart.line_items do |line_item|
  json.partial! '/api/v1/line_item/line_item', locals: { line_item: line_item, bmgnx_free_message_hash: line_item_disc_message}
end
json.hex_symbol @hex_symbol
json.symbol @symbol
json.string_symbol @currency_symbol
json.item_total cart.item_total(@rate)
json.item_total_without_addons cart.items_total_without_addons(@rate)
json.discount cart.total_discounts_currency(@rate)
json.additional_discount_offer messages[0]
json.promotional_discount_offer messages[1]
json.messages messages.compact
json.imp_message @imp_msg
json.bmgn_message @cart.get_bmgnx_notice(with_scheme_name: true)