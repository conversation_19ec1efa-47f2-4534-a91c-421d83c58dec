json.quick_cod do
  json.charges_available @response[:cod_charges_available]
  json.available @response[:cod_designs_response][:cod]
  json.not_available_design_ids @response[:cod_designs_response][:non_cod_designs]
  json.otp_verified @response[:otp_verified]
  json.valid_phone @response[:valid_phone] == 0 ? "Mobile Number doesn't seem valid." : ''
end
json.cart do
  json.partial! 'cart', locals: { cart: @cart, messages: @messages}
  json.total_cod_charges @response[:total_cod_charges]
  json.grand_total @cart.total_currency(@rate, @country_code) + @response[:total_cod_charges]
end