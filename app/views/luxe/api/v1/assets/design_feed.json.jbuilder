json.results @designs.count
json.total_pages @designs.total_pages
json.previous_page @designs.prev_page
json.next_page @designs.next_page
json.designs @designs do |design|
  if design.designer && design.images.present?
    json.cache! ["api/v1/designs/preview_design_#{@country_code}", design], expires_in: API_CACHE_LIFESPAN.minutes do
      json.partial! 'api/v1/designs/preview', locals: { design: design, all_images: true }
    end
    json.cache! ["feed_designer_details_#{design.designer.id}", design.designer], expires_in: API_CACHE_LIFESPAN.minutes do
      json.partial! 'designer', locals: { designer: design.designer }
    end
    json.wishlist_count (count = design.wishlists_count.to_i) < 10 ? count + WISHLIST_COUNT_OFFSET.to_i : count
    json.wishlist_id nil #@wishlist_designs[design.id].try(:first).try(:id)
    json.design_share_url "https://#{DESKTOP_SITE_URL}/designers/#{design.designer.cached_slug}/designs/#{design.cached_slug}?utm_source=android&utm_medium=app&utm_campaign=app_share"
  end
end
