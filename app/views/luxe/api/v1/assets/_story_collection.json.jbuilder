json.cache! ["api/v1/story_collection#{@country_code}/#{@symbol}/#{@staged_app}/", story_collection], expires_in: API_CACHE_LIFESPAN.minutes do
  #To-Do need to move this in controller with lazy behaviour
  ActiveRecord::Associations::Preloader.new.preload(story_collection, designs: [:dynamic_price_for_current_country,:designer,:master_image,:variants,:categories])
  next unless story_collection.designs.present?
  json.timer STORY_COLLECTION_CONSTANT['Story_timer_rate']
  json.story_validation_period STORY_COLLECTION_CONSTANT['Stories_validation_period']
  json.title story_collection.title
  json.associated_story_collection_id story_collection.id
  json.total_stories story_collection.designs.length
  json.stories story_collection.designs do |design|
    json.partial! 'api/v1/designs/preview', locals: { design: design, all_images: false }
  end
end