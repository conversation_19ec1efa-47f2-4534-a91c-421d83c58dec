json.APPSEE_ACTIVATED APPSEE_ACTIVATED
json.UNINSTALLIO_ACTIVATED UNINSTALLIO_ACTIVATED
json.NOTIFICATION_THRESHOLD NOTIFICATION_THRESHOLD
json.BRANCH_ACTIVATED BRANCH_ACTIVATED
json.REFERRALS_ACTIVE @referrals_active.to_s
json.REFERRALS_AMT_VALID @referrals_amt_valid.to_s
json.REFERRAL_TYPE (REFERRAL_TYPES - ['new_user_wallet']).first
json.FIREBASE_REMOTE_CONFIG_CACHE FIREBASE_REMOTE_CONFIG_CACHE
json.LATEST_APP_VERSIONS JSON.parse(LATEST_APP_VERSIONS)
json.QUICK_COD_ENABLE (@country_code == 'IN' && QUICK_COD == 'true').to_s
json.RETURN_ENABLE (RETURN_ENABLE == 'true').to_s
json.promotion_message @offer_message.present? ? ActionController::Base.helpers.strip_tags(@offer_message) : nil
json.story_validation_period STORY_COLLECTION_CONSTANT['Stories_validation_period']
if WHATSAPP_ORDERS_ACTIVE == 'true'
  json.WHATSAPP_CONTACTS JSON.parse(WHATSAPP_CONTACTS) do |contact|
    json.display_name contact['display_name']
    json.mobile_number contact['mobile_number']
    json.email_id contact['email_id']
    json.job_title contact['job_title']
    json.company "Mirraw.com"
  end
end