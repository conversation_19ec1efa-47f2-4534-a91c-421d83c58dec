if params[:search] == 'true' && @designs.is_a?(Hash)
  @designs['designs'].each{ |design| design.merge!({
    like_id: @like_designs[design['id']].try(:first).try(:id),
    liked: @like_designs[design['id']].try(:first).try(:like)
  })}
  json.results @designs['results']
  json.total_pages @designs['total_pages']
  json.previous_page @designs['previous_page']
  json.next_page @designs['next_page']
  json.designs @designs['designs']
else
  json.results @designs.count
  json.total_pages @designs.total_pages
  json.previous_page @designs.prev_page
  json.next_page @designs.next_page
  json.designs @designs do |design|
    if design.designer && design.images.present?
      json.cache! ["api/v1/designs/preview_design_#{@country_code}", design], expires_in: API_CACHE_LIFESPAN.minutes do
        json.partial! 'api/v1/designs/preview', locals: { design: design, all_images: false }
      end
      json.like_id @like_designs[design.id].try(:first).try(:id)
      json.liked @like_designs[design.id].try(:first).try(:like)
    end
  end
end
