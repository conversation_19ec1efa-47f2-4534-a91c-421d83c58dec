json.cache! "api/v1/cart_coupons#{@country_code}#{@app_source}", expires_in: API_CACHE_LIFESPAN.minutes do
    json.data @coupons do |coupon|
        json.id coupon.id
        json.code coupon.code
        json.percent_off coupon.percent_off
        json.flat_off coupon.flat_off
        json.min_amount (coupon.min_amount/@rate).round(2)
        json.currency_symbol @currency_symbol
        json.designer_id coupon.designer_id
        json.designer_name coupon.designer_name
        json.designer_photo coupon.designer_photo
        json.coupon_type coupon.coupon_type
        json.end_date coupon.end_date.to_date.strftime('%d %b %Y')
    end
end
