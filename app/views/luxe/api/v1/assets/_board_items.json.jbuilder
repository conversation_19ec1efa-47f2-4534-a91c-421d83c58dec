json.board_items board_items do |board_item|
  unless board_item.photo.present? && board_item.photo_processing
    json.name (board_item.try(:name) || board_item.try(:title))
    json.id board_item.id
    json.content_type board_item.photo.content_type
    json.photo do
      json.sizes do
        board_item.class::IMAGE_STYLES.each do |style|
          json.set! style, board_item.photo(style)
        end
      end
    end
    json.grade board_item.grade
    unless is_story_collection
      # added for backward compatibility for nested landing page 
      if ((@app_source == 'Android' && request.headers['App-Version']<'2.0.0') || (@app_source == 'iOS' && request.headers['App-Version']<'2.5.0')) && board_item.has_attribute?("alternate_link") && board_item.alternate_link? 
        json.link board_item.alternate_link
      else
        json.link board_item.link
      end
      # added for backward compatibility for nested landing page 
      if ((@app_source == 'Android' && request.headers['App-Version']<'2.0.0') || (@app_source == 'iOS' && request.headers['App-Version']<'2.5.0')) && board_item.has_attribute?("alternate_link") && board_item.alternate_link?
        json.key search_key(board_item.alternate_link_type)
      else
        json.key search_key(board_item.link_type)
      end
      # added for backward compatibility for nested landing page 
      if ((@app_source == 'Android' && request.headers['App-Version']<'2.0.0') || (@app_source == 'iOS' && request.headers['App-Version']<'2.5.0')) && board_item.has_attribute?("alternate_link") && board_item.alternate_link?
        json.value search_value(board_item.alternate_link_type, board_item.alternate_link_value, board_item.alternate_link)
      else  
        json.value search_value(board_item.link_type, board_item.link_value, board_item.link)
      end
    end
  end
end