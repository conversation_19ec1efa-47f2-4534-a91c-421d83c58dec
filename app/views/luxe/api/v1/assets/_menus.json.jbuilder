json.menus menu do |m|
  json.title fix_title(m.title)
  json.link m.link
  json.key search_key(m.link_type)
  json.value m.link.present? ? search_value(m.link_type, m.link_value, m.link.strip) : nil
  json.menu_columns m.menu_columns do |col|
    json.title col.title.present? ? fix_title(col.title) : 'Category'
    json.link col.link
    json.key search_key(col.link_type)
    json.value col.link.present? ? search_value(col.link_type, col.link_value, col.link.strip) : nil
    json.menu_items col.menu_items do |item|
      json.title fix_title(item.title)
      json.key search_key(item.link_type)
      json.value item.link.present? ? search_value(item.link_type, item.link_value, item.link.strip) : nil
      json.link item.link
      if image_url
        json.image_link item.image_file_name.present? ? item.image.url(:med) : nil
      end
    end
  end
end