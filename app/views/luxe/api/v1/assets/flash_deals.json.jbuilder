json.flash_deals do
  json.hex_symbol @hex_symbol
  json.symbol @symbol
  json.string_symbol @currency_symbol
  json.ongoing_fd @ongoing_fd do |end_date, design_promotions|
    json.fd_start_date design_promotions.first.start_date
    json.fd_end_date end_date
    json.ongoing_designs do 
      json.cache_collection! design_promotions, key: "api/v1/designs/flash_design_#{@country_code}", expires_in: API_CACHE_LIFESPAN.minutes do |design_promotion|
        design = design_promotion.design
        if design.designer && design.master_image.present?
          json.partial! 'api/v1/designs/flash_design', locals: { design: design, all_images: false, active_promotions: @active_promotions }
        end
      end
    end
  end
  json.upcoming_fd @upcoming_fd do |end_date, design_promotions|
    json.fd_start_date design_promotions.first.start_date
    json.fd_end_date end_date
    json.upcoming_designs do
      json.cache_collection! design_promotions, key: "api/v1/designs/flash_design_#{@country_code}", expires_in: API_CACHE_LIFESPAN.minutes do |design_promotion|
        design = design_promotion.design
        if design.designer && design.master_image.present?
          json.partial! 'api/v1/designs/flash_design', locals: { design: design, all_images: false, active_promotions: @active_promotions }
        end
      end
    end
  end
end
