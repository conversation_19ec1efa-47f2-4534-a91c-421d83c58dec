json.blocks frontpages do |block|
  json.id block.id
  json.name type == 'landing' ? block.title : block.name
  json.photo do
    json.sizes do
      block.class::IMAGE_STYLES.each do |style|
        json.set! style, type == 'landing' ? block.banner(style) : block.photo(style)
      end
    end
  end
  json.link block.link
  json.grade block.grade
  json.key search_key(block.link_type)
  json.value search_value(block.link_type, block.link_value, block.link)
end