json.cache_if! !STORY_COLLECTION_CONSTANT['Story_queue_shuffle'],["api/v1/story_collections_set#{@country_code}/#{@symbol}"], expires_in: API_CACHE_LIFESPAN.minutes do
  json.total_story_collections @story_collections.length
  json.timer STORY_COLLECTION_CONSTANT['Story_timer_rate']
  json.story_collection @story_collections do |story_collection|
    json.partial! 'api/v1/assets/story_collection', locals: { story_collection: story_collection}
  end
end