json.cache! ["api/v1/front_page_banners#{@country_code}#{@app_source}/#{@symbol}", @banner_sliders], expires_in: API_CACHE_LIFESPAN.minutes do
  json.partial! 'banners', locals: { banner_sliders: @banner_sliders }
end

json.cache! ["api/v1/front_page_blocks#{@country_code}#{@app_source}/#{@symbol}", @frontpages], expires_in: API_CACHE_LIFESPAN.minutes do
  json.partial! 'blocks', locals: { frontpages: @frontpages, type: 'frontpage' }
end