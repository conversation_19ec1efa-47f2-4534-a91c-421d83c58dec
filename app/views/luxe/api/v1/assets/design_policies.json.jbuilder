json.cache! "api/v1/design_policies_#{@country_code}_#{@category}", expires_in: API_CACHE_LIFESPAN.minutes do
  json.shipping do
    json.in_india do
      json.locations DESIGN_POLICIES_SHIPPING_IN_INDIA_LOCATIONS
      json.time DESIGN_POLICIES_SHIPPING_IN_INDIA_TIME
      json.charges DESIGN_POLICIES_SHIPPING_IN_INDIA_CHARGES
    end
    json.out_of_india do
      json.locations Country.pluck(:name)
      json.note "Free shipping applicable subject to campaigns/offers and Order total."
      json.time DESIGN_POLICIES_SHIPPING_OUT_OF_INDIA_TIME
      json.charges "#{@symbol} #{get_price_in_currency(500)} - First 500 grams and #{@symbol} #{get_price_in_currency(200)} - Every additional 500 grams. Total shipping charges basis the weight of items in your order will be reflected on ‘checkout’ page."
    end
  end
  json.stitching do
    json.time DESIGN_POLICIES_STITCHING_TIME
    json.charges "Blouse - #{@symbol} #{get_price_in_currency(B_REGULAR)} (Standard), #{@symbol} #{get_price_in_currency(B_CUSTOM)} (Custom) | Salwar Suits - #{@symbol} #{get_price_in_currency(SS_STANDARD)} (Standard), #{@symbol} #{get_price_in_currency(SS_CUSTOM)} (Custom) | Lehenga - #{@symbol} #{get_price_in_currency(L_STANDARD)} (Standard), #{@symbol} #{get_price_in_currency(L_CUSTOM)} (Custom) | (may vary based on type and fit chosen)"
    json.returns DESIGN_POLICIES_STITCHING_RETURNS
    json.instructions DESIGN_POLICIES_STITCHING_INSTRUCTIONS
  end
  json.payments do
    json.in_india JSON.parse(DESIGN_POLICIES_PAYMENTS_IN_INDIA)
    json.out_of_india JSON.parse(DESIGN_POLICIES_PAYMENTS_OUT_OF_INDIA)
  end
  json.returns do
    if @show
      json.policy LUXURY_CATEGORY_POLICY
    else
      json.time DESIGN_POLICIES_RETURNS_TIME
      json.process DESIGN_POLICIES_RETURNS_PROCESS
      json.refunds_or_replacements DESIGN_POLICIES_RETURNS_REFUNDS_OR_REPLACEMENTS
    end
  end
end