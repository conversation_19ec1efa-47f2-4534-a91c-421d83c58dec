json.cache_if! @can_cache_full_response, "api/v1/dynamic_homepage#{@country_code}#{@app_source}/#{params[:page]}/#{@staged_app}", expires_in: API_CACHE_LIFESPAN.minutes do
  json.total_pages @boards.total_pages
  json.previous_page @boards.prev_page
  json.next_page @boards.next_page
  json.story_validation_period STORY_COLLECTION_CONSTANT['Stories_validation_period']
  json.seo_post do
    json.partial! 'api/v1/store/seo_post', locals: {seo: @seo}
  end
  json.homepage_widgets do
    json.array!(@story_board) do |board|
      json.id board.id
      json.type board.board_type
      json.grade board.grade
      json.name board.name
      json.display_items_name board.display_items_name
      json.aspect_ratio board.banner_sliders.first.try(:aspect_ratio).to_f.round(3)
      board_items = board.story_collections.live.latest.swiped_story_collection(visited_collections: params[:visited_sc])
      json.partial! 'board_items', locals: { board_items: board_items || [], is_story_collection: board.board_type.to_sym == :story_collection}
    end
    json.cache! "api/v1/dynamic_homepage_non_story_widgets#{@country_code}#{@app_source}/#{params[:page]}/#{@staged_app}", expires_in: API_CACHE_LIFESPAN.minutes do
      json.array!(@boards) do |board|
        json.id board.id
        json.type board.board_type
        json.grade board.grade
        json.name board.name
        json.display_items_name board.display_items_name
        json.aspect_ratio board.banner_sliders.first.try(:aspect_ratio).to_f.round(3)
        if board.timer
          json.promotion_offer_end_time ((offer = PromotionPipeLine.get_active_promotion(@country_code)).present? && offer.end_date.to_datetime <= 72.hours.from_now) ? offer.end_date.to_datetime.strftime('%Q') : nil
        end
        board_items = case board.board_type.to_sym
                      when :frontpage
                        board.frontpages.graded
                      when :tag_slider
                        board.tag_sliders.graded
                      when :auto_scrollable_banner, :user_scrollable_banner, :static_banner
                        board.banner_sliders.graded
                      end
        json.cache! ["api/v1/board_items#{@country_code}#{@app_source}/#{@symbol}/#{@staged_app}", board_items], expires_in: API_CACHE_LIFESPAN.minutes do
          json.partial! 'board_items', locals: { board_items: board_items || [], is_story_collection: board.board_type.to_sym == :story_collection}
        end

        design_items = case board.board_type.to_sym
                       when :best_seller_designs
                         Bestseller.get_bestsellers(@country_code)
                       when :hand_picked_designs
                         board.designs.in_stock.includes(:designer, :master_image, :variants).valid_designs
                       end.to_a
        json.design_items do
          json.cache_collection! design_items, key: "api/v1/designs/preview_design_#{@country_code}", expires_in: API_CACHE_LIFESPAN.minutes do |design|
            json.partial! 'api/v1/designs/preview', locals: { design: design, all_images: false }
          end
        end
        json.link board.link
        json.key search_key(board.link_type)
        json.value search_value(board.link_type, board.link_value, board.link)
      end
    end
  end
end