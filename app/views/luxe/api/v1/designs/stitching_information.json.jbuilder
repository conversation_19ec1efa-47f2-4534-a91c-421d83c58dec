json.cache! "api/v1/designs/stitching_images", expires_in: API_CACHE_LIFESPAN.minutes do
  json.logo_1 IMAGE_PROTOCOL + image_url("stitching_cart.png")[2..-1]
  json.logo_2 IMAGE_PROTOCOL + image_url("stitching_img.png")[2..-1]
  json.logo_3 IMAGE_PROTOCOL + image_url("mail.png")[2..-1]
  json.logo_4 IMAGE_PROTOCOL + image_url("truck.png")[2..-1]
  json.image_1 IMAGE_PROTOCOL + image_url("blouse_size.jpg")[2..-1]
  json.image_2 IMAGE_PROTOCOL + image_url("style.jpg")[2..-1]
  json.image_3 IMAGE_PROTOCOL + image_url("petticoat.jpg")[2..-1]
  json.image_4 IMAGE_PROTOCOL + image_url("pico.jpg")[2..-1]
  json.image_5 IMAGE_PROTOCOL + image_url("salwar_size.jpg")[2..-1]
  json.image_6 IMAGE_PROTOCOL + image_url("lehenga_size.jpg")[2..-1]
  json.stitching_information STITCHING_INFORMATION
end
json.cache! "api/v1/designs/stitching_information_#{@symbol}", expires_in: API_CACHE_LIFESPAN.minutes do
  json.price_1 get_price_with_symbol(get_price_in_currency(B_REGULAR), @symbol)
  json.price_2 get_price_with_symbol(get_price_in_currency(B_CUSTOM), @symbol)
  json.price_3 get_price_with_symbol(get_price_in_currency(SS_STANDARD), @symbol)
  json.price_4 get_price_with_symbol(get_price_in_currency(SS_CUSTOM), @symbol)
  json.price_5 get_price_with_symbol(get_price_in_currency(L_STANDARD), @symbol)
  json.price_6 get_price_with_symbol(get_price_in_currency(L_CUSTOM), @symbol)
  json.price_7 get_price_with_symbol(get_price_in_currency(PETTICOAT), @symbol)
  json.price_8 get_price_with_symbol(get_price_in_currency(FNP), @symbol)
end