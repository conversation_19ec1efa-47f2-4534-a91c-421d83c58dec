ActiveRecord::Associations::Preloader.new.preload(@recommended_designs_unbxd, [:master_image,:designer,:categories,:variants])
valid_designs = @recommended_designs_unbxd.select(&:valid_design?)
json.designs do
  json.cache_collection! valid_designs, key: "recommended_designs_#{@country_code}", expires_in: API_CACHE_LIFESPAN.minutes do |design|
    json.partial! 'api/v1/designs/preview', locals: { design: design, all_images: false }
  end
end