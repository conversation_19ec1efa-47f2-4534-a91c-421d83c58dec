count = -1
json.hot_selling_designs do
  json.results @designs.count
  json.total_pages @designs.total_pages
  json.previous_page @designs.prev_page
  json.next_page @designs.next_page
  json.designs @designs do |design|
    if design.designer
      json.cache! ["api/v1/designs/preview_design_#{@country_code}", design], expires_in: API_CACHE_LIFESPAN.minutes do
        json.partial! 'preview', locals: { design: design, all_images: false }
      end
      json.sell_count @count[(count+=1)]
    end
  end
end