json.cache! ["api/v1/#{Design.country_code}/#{@symbol}/#{@design.id}", @design], expires_in: API_CACHE_LIFESPAN.minutes do
  json.cache! ["api/v1/details/#{@design.id}", @design], expires_in: API_CACHE_LIFESPAN.minutes do
    ActiveRecord::Associations::Preloader.new.preload(@design, [property_values: :property])
    json.partial! '/api/v1/designs/details', locals: { design: @design }
  end
  json.stitching_available @design.stitchable?(@country_code)
  json.promotion_offer_end_time @design.applicable_promotions_end_date(PromotionPipeLine.get_active_promotion(@country_code), @country_code)
  json.non_rts_time (@addon_ship_time.presence || @number_of_days)
  json.ready_to_ship false #@design.rts_available(@actual_country)
  json.sor_available @design.sor_available?
  json.variant_sor_available @design.variants.any?{|v| v.sor_available?}
  json.unstitched_in_warehouse @design.size_in_warehouse?
  json.rts_conditional_msg "Delivery date may vary if the order is to be shipped outside the #{@actual_country}"
  json.rts_tnc Design.rts_tnc
  json.hex_symbol @hex_symbol
  json.symbol @symbol
  json.string_symbol @currency_symbol
  json.cached_slug @design.cached_slug
  json.review_count @design.reviews.comments.approved.size
  json.designer_cached_slug @design.designer.cached_slug
  json.ga_list @ga_list
  # manipulating the code for manyavar pricing inconsistency 
  json.price ( @design.designer.name == "Manyavar" ) ? @design.effective_price_currency(@rate) :  @design.price_currency(@rate)
  json.designable_type @design.designable_type
  json.discount_price @design.effective_price_currency(@rate)
  json.inr_discount_price @design.effective_price_currency(1)
  json.discount_percent ( @design.designer.name == "Manyavar") ? 0 : @design.effective_discount(PromotionPipeLine.active_promotions)
  json.design_share_url "https://#{DESKTOP_SITE_URL}/designers/#{@design.designer.cached_slug}/designs/#{@design.cached_slug}?utm_source=android&utm_medium=app&utm_campaign=app_share"
  json.shipping_charges_info nil #@design.get_shipping_info(@rate, @actual_country, @cart, @symbol)
  json.product_offer @design.product_offer(@country_code, with_qpm: true)
  json.mirraw_recommended @design.mirraw_recommended?
  json.mirraw_certified @design.mirraw_certified
  json.premium @design.premium
  json.cod_avail @design.cod_available_for_design(@country_code, @actual_country)
  json.addon_products @addon_products do |product|
    json.partial! '/api/v1/designs/addon_product', locals: { design: product }
  end
end
json.cache! ["api/v1/#{Design.country_code}/#{@symbol}/#{@design.id}/#{User.app_source}/#{request.headers['App-Version']}", @design], expires_in: API_CACHE_LIFESPAN.minutes do
  json.offers @design.promotion_messages(@country_code, @rate, @symbol, @app_source.split('-')[0], request.headers['App-Version'])
  json.current_offers @design.product_current_offers(@country_code,@actual_country, @app_source.split('-')[0], request.headers['App-Version'])
end
if STITCHING_ENABLE == 'true' && (@design.designable_type != 'Kurti' || (@design.designable_type == 'Kurti' && (request.env["sent_from_web_mobile"] || request.headers["Sent-from-Web-Mobile"])))
  json.cache! ["api/v1/#{Design.country_code}/#{@symbol}/addon_types/not_#{region_is_not?}/#{User.app_source}/#{!!Design.plus_size_enable?(request)}/#{with_or_without_custom_addon(@app_source.downcase, request.headers['App-Version'], @design.designable_type)}", @design], expires_in: API_CACHE_LIFESPAN.minutes do
    json.partial! '/api/v1/designs/addon_types', locals: { design: @design }
  end
end
json.partial! '/api/v1/designs/variants', locals: { design: @design }
  json.latest_reviews (((@is_review_exp || REVIEW_DISPLAY == 'true' || true) && @design.average_rating >= 0.0) ? @design.reviews.preload(:user).comments.approved.random_postive_rating : []) do |review|
  json.partial! '/api/v1/reviews/review_details', locals: { review: review, curr_user: @current_user }
end
json.state @design.available? ? @design.state : 'sold_out'
json.average_rating @design.average_rating
json.designer_rating REVIEW_DISPLAY == 'true' ? @design.designer.average_rating : nil
json.review_opinion @design.review_opinion
json.total_review @design.total_review
json.rating @review.try :rating
json.review @review.try :review
json.wishlist_id @current_user ? @design.wishlist_for_user(@current_user.id).try(:id) : nil
json.wishlist_count @wishlist_count
json.can_review current_account.try(:user) ? current_account.user.can_review(@design.id) > 0 : false
json.stitching_testimonials @design.stitching_testimonials(@country_code)
json.estimated_delivery_days @number_of_days
json.video_available @design.has_video
