json.id design.id
json.title design.title
json.description design.description
json.specification design.specification
json.quantity design.quantity
json.hex_symbol @hex_symbol
json.symbol @symbol
json.design_path designer_design_path(design.designer, design)
json.cached_slug design.cached_slug
json.state design.available? ? design.state : 'sold_out'
json.designer_cached_slug design.designer.cached_slug
json.price design.price_currency(@rate)
json.designable_type design.designable_type
json.discount_price design.effective_price_currency(@rate)
json.discount_percent design.effective_discount(PromotionPipeLine.active_promotions)
json.cache! ['api/v1/images', design] do
  if design.master_image
    json.master_image design.master_image.photo(:small)
  end
  json.partial! '/api/v1/designs/images', locals: { design: design }
end
json.partial! '/api/v1/designs/variants', locals: { design: design }