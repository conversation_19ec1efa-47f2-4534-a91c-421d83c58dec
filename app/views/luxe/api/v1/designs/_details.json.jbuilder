json.id design.id
json.title design.title
json.description design.description
json.specification design.specification
json.type design.categories[0].try(:title)
json.category_name design.categories[0].try(:name)
json.package_details design.package_details
json.quantity design.quantity
json.sku design.design_code
if (quantity = design.stitchable_quantity) > 1
  json.stitching_combo_message "The below dropdown prices includes stitching price for #{quantity} products"  
end
json.designer do
  json.id design.designer.id
  json.name design.designer.name
  json.image design.designer.photo_file_name ? design.designer.photo(:small) : image_url("fb_default.jpg")
  json.positive_reviews  REVIEW_DISPLAY == 'true' ? design.designer.review_text : ' '
  json.pickup_city design.designer.pickup_location
end
json.categories design.categories do |category|
  json.id category.id
  json.name category.name
  json.weight category.weight
end
json.parent_categories design.category_parents_name
json.cache! ['api/v1/images', design] do
  if design.master_image
    json.master_image design.master_image.photo(:small)
  end
  json.partial! '/api/v1/designs/images', locals: { design: design }
end
json.specifications do
  json.product_id design.id
  json.package_details design.package_details
  json.specification design.specification
  json.international_shipping design.can_ship_international?
  json.properties properties(design.property_values)
  json.designable designable_attributes(design.designable)
  json.weight design.weight_in_imperial if design.weight_in_imperial.present?
  json.region design.region
  json.plus_size_max_bust design.system_plus_size ? design.get_max_bust_size : nil
end
