json.results @designs.count
json.total_pages @designs.total_pages
json.previous_page @designs.prev_page
json.next_page @designs.next_page
json.designs @designs do |design|
  json.design do
    d = design.last.first.design
    json.id d.id
    json.title d.title
    json.average_rating d.average_rating
    json.image d.master_image.photo('small_m')
    json.total_reviews design.last.count
    json.reviews design.last do |review|
      json.user do
        json.partial! 'api/v1/users/user', locals: { user: review.user, curr_user: @curr_user } if review.user.present?
      end
      json.review do
        json.id review.id
        json.rating review.rating
        json.review review.review
        json.time_ago distance_of_time_in_words_to_now(review.updated_at) + " ago"
      end
    end
  end
end