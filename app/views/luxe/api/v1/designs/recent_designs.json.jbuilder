json.recent_design do
  json.results @history.count
  json.total_pages @history.total_pages
  json.previous_page @history.prev_page
  json.next_page @history.next_page
  json.designs do
    json.cache_collection! @history.valid_designs, key: "api/v1/designs/preview_design_#{@country_code}", expires_in: API_CACHE_LIFESPAN.minutes do |design|
      json.partial! 'api/v1/designs/preview', locals: { design: design, all_images: false }
    end
  end
end