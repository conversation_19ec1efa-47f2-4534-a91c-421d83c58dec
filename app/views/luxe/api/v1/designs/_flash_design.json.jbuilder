json.id design.id
json.title design.title
json.hex_symbol @hex_symbol
json.symbol @symbol
json.string_symbol @currency_symbol
json.design_path designer_design_path(design.designer, design)
json.price design.price_currency(@rate)
json.discount_price design.effective_price_currency(@rate)
json.fd_discount_price design.fd_effective_price_currency(@rate)
json.fd_discount_percent design.fd_effective_discount(active_promotions)
json.discount_percent design.effective_discount(active_promotions)
json.inr_discount_price Design.inr_value(design.effective_price, @country_code, @rate)
json.cached_slug design.cached_slug
json.state design.available? ? design.state : 'sold_out'
json.stock design.quantity
json.brand design.designer.cached_slug
if @country_code == 'IN'
  json.grade design.grade
else
  json.grade design.international_grade
end
json.partial! '/api/v1/designs/master_image', locals: { design: design }
