ActiveRecord::Associations::Preloader.new.preload(@similar_designs_unbxd, [:master_image,:designer,:categories,:variants])
valid_designs = @similar_designs_unbxd.select(&:valid_design?)
json.designs do
  json.cache_collection! valid_designs, key: "api/v1/designs/preview_design_#{@country_code}", expires_in: API_CACHE_LIFESPAN.minutes do |design|
    json.partial! 'api/v1/designs/preview', locals: { design: design, all_images: false }
  end
end