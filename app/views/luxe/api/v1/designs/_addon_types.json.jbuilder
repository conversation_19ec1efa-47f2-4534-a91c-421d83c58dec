addon_type_array = []
rts = design.sor_available?
unstitched_in_warehouse = design.size_in_warehouse? if rts
plus_size_addons_show = Design.plus_size_enable?(request)
unstitched_variant = (design.designable_type == 'Kurti' && (request.env['sent_from_web_mobile'] || request.headers["Sent-from-Web-Mobile"]) ? design.unstitched_variant : nil)
json.addon_types design.addons_association_not_for(region_is_not?) do |at|
  unless addon_type_array.include?(at.name)
    addon_type_array << at.name.strip
    json.name at.name
    json.position at.position
    json.type_of_addon at.type_of_addon
    addon_type_values_array = []
    json.plus_size_info PLUS_SIZE['PLUS_SIZE_INFO'] if plus_size_addons_show
    json.plus_size_enable plus_size_addons_show
    json.plus_size_range PLUS_SIZE['PLUS_SIZE_RANGE'] if plus_size_addons_show
    json.addon_type_values at.addon_type_values do |atv|
      next if (atv.description == 'addons_with_variants' && !request.env["sent_from_web_mobile"] && !request.headers["Sent-from-Web-Mobile"]) || (Design::STITCHING_WITH_VARIANT_DESIGNABLE_TYPES.include?(design.designable_type) && ((!unstitched_variant.present? && atv.description == 'addons_with_variants') || (unstitched_variant.present? && atv.description != 'addons_with_variants')))
      unless addon_type_values_array.include?(atv.name)
        addon_type_values_array << atv.name.strip
        json.id atv.id
        json.p_name atv.name
        if atv.name.downcase.include?('custom')
          stitching_msg = 'Note: Measurement form will be emailed to you once order is placed.'
          stitching_msg+= " Sizes are supported upto Max Bust Size #{design.get_max_bust_size} Inch." if design.system_plus_size
          json.msg stitching_msg
        end
        json.position atv.position
        json.description atv.description
        #json.prod_time (ship_time = atv.prod_time + (atv.prod_time == 0 ? @number_of_days : (@addon_ship_time.presence || @number_of_days)))
        json.prod_time atv.prod_time
        if (design.ready_to_ship? || unstitched_in_warehouse)
          json.delivery_days (design.delivery_date_for_rts(@actual_country) + atv.prod_time).round
          json.rts_prod_time (design.delivery_date_for_rts(@actual_country) + atv.prod_time).round
          json.rts atv.prod_time == 0 ? 'true' : 'false'
        else
          json.delivery_days (@addon_ship_time.presence || @number_of_days) + atv.prod_time
          json.rts_prod_time ((rts ? design.delivery_date_for_rts(@actual_country) : @number_of_days) + atv.prod_time).round
          json.rts 'false'
        end
        json.price atv.price_currency(@rate, design)
        rts_sizes = []
        if Design::STITCHING_ALLOWED_DESIGNABLE_TYPES.include?(design.designable_type) && ['Standard Stitching','Regular Blouse Stitching'].include?(atv.name) && (sizes = SizeChart.active_sizes).present?
          std_sizes_allowed = sizes.collect(&:size).select{|sz| design.check_if_plus_size_serviceable(sz)}
          json.standard_stiching_sizes std_sizes_allowed
          json.size_chart SIZE_CHART_TABLE["#{design.designable_type}-Size"]
          json.stitching_info_image design.designable_type == 'SalwarKameez' ? image_url("kameez.jpg") : nil
          json.show_stitching_bust_message design.designable_type.try(:downcase) == 'salwarkameez' ? true : false
          json.standard_stitch_sizes sizes do |size|
            next if std_sizes_allowed.exclude?(size.size)
            json.id size.id
            json.size_name size.size
            if rts && design.size_in_warehouse?(size.size_bucket_id)
              json.prod_time 0
              json.delivery_days design.delivery_date_for_rts(@actual_country).round
              json.rts 'true'
              rts_sizes.push(size.size)
            elsif rts && unstitched_in_warehouse
              json.prod_time atv.prod_time
              json.delivery_days (design.delivery_date_for_rts(@actual_country) + atv.prod_time).round
              json.rts 'false'
            else
              json.prod_time atv.prod_time
              json.delivery_days (@addon_ship_time.presence || @number_of_days) + atv.prod_time
              json.rts 'false'
            end
          end
          json.rts_sizes rts_sizes
        end
        addon_option_types = atv.get_addon_option_types(@app_source.downcase, request.headers['App-Version'],design.designable_type)
        json.addon_option_types addon_option_types do |addon_option_type_id, addon_option_values|
          addon_option_type = addon_option_values.first.addon_option_type
          is_custom_option = atv.name.downcase.include?('custom')
          is_custom_bust_size_option = (is_custom_option && addon_option_type.p_name.downcase.include?('bust size'))
          # next if is_custom_bust_size_option
          next unless addon_option_type.published
          next if ['select plus size','select fabric color'].include?(addon_option_type.p_name.to_s.downcase) && !plus_size_addons_show
          next if addon_option_type.option_type == 'checkbox' && design.designable_type == 'SalwarKameez' && is_custom_option && (ANARKALI_CATEGORY_FOR_STANDARD_ADDON & design.categories.collect(&:id)).blank?
          unless addon_option_type.p_name == 'Select Blouse Size'
            if addon_option_type.p_name.try(:downcase) == 'select your height' && design.designable_type.try(:downcase) == 'salwarkameez'
              stitching_height_value, stitching_height_message, stitching_height_images = design.get_addon_img_and_msg_to_show_for_height 
              json.stitching_height_values stitching_height_value
              json.stitching_height_messages stitching_height_message
              json.stitching_height_images stitching_height_images
            end
            json.id addon_option_type.id
            json.name addon_option_type.name
            json.p_name addon_option_type.p_name
            json.position addon_option_type.position
            json.published addon_option_type.published
            json.option_type addon_option_type.option_type
            json.price addon_option_type.price_currency(@rate, design)
            valid_addon_option_values = (is_custom_bust_size_option ? addon_option_values.select{|aov| design.check_if_plus_size_serviceable(aov.p_name.to_i)} : addon_option_values)
            json.addon_option_values valid_addon_option_values do |aov|
              json.id aov.id
              json.p_name aov.p_name
              json.name aov.name
              json.position aov.position
              json.hexcode aov.hexcode
            end
          end
        end
      end
    end
  end
end
