- content_for :page_specific_css do
  = stylesheet_link_tag 'request_phone_number'
= javascript_include_tag 'request_phone_modal'
%div#update-message.hide
= form_for account, remote: true, html: {id: 'update_phone_form'} do |f|
  .row.collapse
    .columns.small-2.dial_code_block
      = f.text_field :dial_code, label: false, value: '+91', readonly: true, class: 'dial_code_text_field'
    .columns.medium-2.phone_number_block
      - billing_address_phone = account.user.try(:default_address).try(:phone)
      - if billing_address_phone.present?
        - billing_address_phone.gsub!("+91", "")
      = f.telephone_field :phone, placeholder: 'Enter a 10 digit mobile number ', required: true, label: false, pattern: "^[6-9][0-9]{9}$", value: billing_address_phone, class: 'tel_field'
    = f.submit 'UPDATE', class: 'update_btn', data: { disable_with: "Please wait..." }