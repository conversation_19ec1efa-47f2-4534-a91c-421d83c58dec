= javascript_include_tag 'luxe_catalog', :defer => true, 'data-turbolinks-track' => 'reload'
- if @seo.present? && !params[:page].present?
  - title @seo.title.to_s
  - description @seo.description if @seo.description.present?
  - keywords @seo.keyword if @seo.keyword.present?
- elsif params[:kind].present? && params[:page].present?
  - title "Page #{params[:page]} - Buy #{params[:kind].to_s.camelize} Online"
  - og_title "Page #{params[:page]} - Buy #{params[:kind].to_s.camelize} Online"
  - description "Page #{params[:page]} - Shop #{params[:kind].to_s.camelize} made of top brands with beautiful designer collections at best prices on Muzai"
  - keywords "#{params[:kind]}, #{params[:kind]} online"
- elsif params[:kind].present?
  - title "#{params[:kind].to_s.camelize} – Buy #{params[:kind].to_s.camelize} Online for #{@category.cached_self_and_ancestors_name.first.to_s.camelize} @ Best Prices"
  - og_title "#{params[:kind].to_s.camelize} – Buy #{params[:kind].to_s.camelize} Online for #{@category.cached_self_and_ancestors_name.first.to_s.camelize} @ Best Prices"
  - description "Shop #{params[:kind].to_s.camelize} online made of top brands with beautiful designs & stylish collections available at Muzai at best prices"
  - keywords "#{params[:kind]}, #{params[:kind]} online"
- elsif params[:id].present? && params[:page].present?
  - title "Page #{params[:page]} - #{params[:id].to_s.camelize}"
  - og_title "Page #{params[:page]} - #{params[:id].to_s.camelize}"
  - description "Page #{params[:page]} - Browse designer #{params[:id].to_s.camelize} Products on Muzai"
  - keywords "#{params[:id]}"
- elsif params[:id].present?
  - title "#{params[:id].to_s.camelize} on Muzai"
  - og_title "#{params[:id].to_s.camelize} on Muzai"
  - description "Browse designer #{params[:id].to_s.camelize} Products on Muzai & Get amazing festive discounts including low-cost shipping and On Time Delivery."
  - keywords "#{params[:id]}"


/ Global site tag (gtag.js) - Google Ads: 10987265357
-# %script{:async => "", :src => "https://www.googletagmanager.com/gtag/js?id=AW-10987265357"}
-# :javascript
  -# window.dataLayer = window.dataLayer || [];
  -# function gtag(){dataLayer.push(arguments);}
  -# gtag('js', new Date());
-# 
  -# gtag('config', 'AW-10987265357');
-# / Event snippet for Category Page Mirraw Luxe conversion page
-# :javascript
  -# gtag('event', 'conversion', {'send_to': 'AW-10987265357/SCk7CP2Ttd4DEM26kfco'});

- content_for :page_specific_css do
  / = stylesheet_link_tag 'luxe/global', rel: "preload", as: "style",  onload: "this.onload=null;this.rel='stylesheet'"
  = stylesheet_link_tag 'luxe/catalog' #, rel: "preload", as: "style",  onload: "this.onload=null;this.rel='stylesheet'"
  = stylesheet_link_tag 'luxe/product-filter' #, rel: "preload", as: "style",  onload: "this.onload=null;this.rel='stylesheet'"
%header.mobile-header-component-container
  .mobile-fixed-header-wrapper
    -if !is_mobile_view?
      .mobile-message-block.offer-txt.destop
        - if @offer_message.present?
          =raw @offer_message
        - else
          =raw "For Personal Assistance, you can WhatsApp us on"
          = link_to "+91 9137407527. ",'https://api.whatsapp.com/send?phone=919137407527&text=Hello', target: '_blank', style: 'color:#f56a6a;'
        = render partial: 'luxe/pages/deal_timer_mobile'

    .flex-container
      .flex-item-1
        %button.openSideNav.action-btn.action-hamburger-btn
          / prettier-ignore
          %svg#Back_arrow{"data-name" => "Back arrow", :height => "24", :viewbox => "0 0 24 24", :width => "24", :xmlns => "http://www.w3.org/2000/svg"}
            %g#Rectangle_21663{"data-name" => "Rectangle 21663", :opacity => "0", :stroke => "#f5f5f5", "stroke-width" => "1"}
              %rect{:height => "24", :stroke => "none", :width => "24"}
              %rect{:fill => "none", :height => "23", :width => "23", :x => "0.5", :y => "0.5"}
            %g#Iconly_Light-Outline_Document{"data-name" => "Iconly/Light-Outline/Document", :transform => "translate(-2.589 -2.382)"}
              %g#Document{:transform => "translate(6.746 7.11)"}
                %path#Combined-Shape{:d => "M18.531,17.513a1.112,1.112,0,0,1,0,2.224H4.858a1.112,1.112,0,0,1,0-2.224Zm0-6.209a1.112,1.112,0,0,1,0,2.224H4.858a1.112,1.112,0,0,1,0-2.224ZM11.909,5.11a1.112,1.112,0,0,1,0,2.224H4.858a1.112,1.112,0,1,1,0-2.224Z", "fill-rule" => "evenodd", :transform => "translate(-3.745 -5.11)"}
      .flex-item-2.header-logo
        %a{href: '/'}
          =image_tag('luxe/logo/muzai.png', alt: 'muzai', class: 'logo-img')
      .flex-item-3
        .dropdown
          %button.action-btn.user-btn{id: 'catalog-profile-btn', class: account_signed_in? ? 'signed-in' : 'not-signed-in'}
            / prettier-ignore
            %svg#Profile_in-active_W_O_Img{"data-name" => "Profile in-active/ W O Img", :height => "24", :viewbox => "0 0 24 24", :width => "24", :xmlns => "http://www.w3.org/2000/svg"}
              %g#Rectangle_24115{"data-name" => "Rectangle 24115", :opacity => "0", :stroke => "#f5f5f5", "stroke-width" => "1"}
                %rect{:height => "24", :stroke => "none", :width => "24"}
                %rect{:fill => "none", :height => "23", :width => "23", :x => "0.5", :y => "0.5"}
              %g#Iconly_Bold_Profile{"data-name" => "Iconly/Bold/Profile", :transform => "translate(0.8 1)"}
                %g#Profile{:transform => "translate(4 2)"}
                  %path#Path_220122{:d => "M7.2,16.5a14.812,14.812,0,0,0,4.562-.528c1.138-.4,1.138-.827,1.138-1.033s0-.643-1.137-1.045A14.612,14.612,0,0,0,7.2,13.357a14.812,14.812,0,0,0-4.562.528c-1.138.4-1.138.827-1.138,1.033s0,.643,1.137,1.045A14.611,14.611,0,0,0,7.2,16.5m0-8.475a3.247,3.247,0,1,0-2.313-.951A3.248,3.248,0,0,0,7.2,8.025M7.2,18C3.3,18,0,17.365,0,14.917s3.317-3.061,7.2-3.061c3.9,0,7.2.634,7.2,3.083S11.083,18,7.2,18Zm0-8.475a4.762,4.762,0,1,1,4.765-4.763A4.747,4.747,0,0,1,7.2,9.525Z", "data-name" => "Path 220122", :transform => "translate(0 0)"}
            .dropdown-content.drpdown-div
              - if account_signed_in?
                = link_to 'Wallet', user_wallet_path
                = link_to 'Profile' , user_profile_path
                = link_to 'Order Details' , '/orders'
                = link_to 'Address', 'user/addresses'
                = link_to 'Logout', destroy_account_session_path, method: :delete

          = link_to user_wishlists_path do
            %button.action-btn.wishlist-btn
              / prettier-ignore
              %svg#icon_wishlist_inactive{"data-name" => "icon/wishlist/inactive", :height => "24", :viewbox => "0 0 24 24", :width => "24", :xmlns => "http://www.w3.org/2000/svg"}
                %rect#Rectangle_23416{"data-name" => "Rectangle 23416", :fill => "#fff", :height => "24", :opacity => "0", :width => "24"}
                %path#Path_197893{:d => "M2.2,8.512a5.593,5.593,0,0,0,1.733,4.261c1.387,1.337,5.981,4.345,6.154,4.512a1.264,1.264,0,0,0,.52.167,1.264,1.264,0,0,0,.52-.167,64.353,64.353,0,0,0,6.154-4.512,5.593,5.593,0,0,0,1.733-4.261A4.579,4.579,0,0,0,14.334,4a4.661,4.661,0,0,0-3.64,1.922A4.734,4.734,0,0,0,6.88,4,4.635,4.635,0,0,0,2.2,8.512Z", "data-name" => "Path 197893", :fill => "none", :stroke => "#1f1f1f", "stroke-linecap" => "round", "stroke-linejoin" => "round", "stroke-width" => "1.5", :transform => "translate(1.393 1.274)"}
          = link_to cart_path do
            %button.action-btn.cart-btn
              %span.cart-count= "#{session[:cart_count]}"
              / prettier-ignore
              %svg#icon_orders{"data-name" => "icon/orders", :height => "24", :viewbox => "0 0 24 24", :width => "24", :xmlns => "http://www.w3.org/2000/svg"}
                %rect#Rectangle_23418{"data-name" => "Rectangle 23418", :fill => "#fff", :height => "24", :opacity => "0", :width => "24"}
                %path#XMLID_1732_{:d => "M17.961,14.189l-2.093,6.593a1.109,1.109,0,0,1-1.057.783H6.73a1.154,1.154,0,0,1-1.076-.724L2.563,12.565H.783A.783.783,0,1,1,.783,11H3.111a.8.8,0,0,1,.743.528L7.043,20h7.435l1.663-5.283H6.926a.783.783,0,1,1,0-1.565H17.217a.778.778,0,0,1,.626.333.759.759,0,0,1,.117.7ZM7.122,22.6a1.269,1.269,0,1,0,.9.372A1.29,1.29,0,0,0,7.122,22.6Zm7.024,0a1.269,1.269,0,1,0,.9.372A1.29,1.29,0,0,0,14.146,22.6Z", :transform => "translate(3 -6.073)"}
    .header-searchbar-wrapper
      = render partial:  'layouts/search_form_luxe'
.mobile-side-navbar-component-container
  #mobileSideNav.mobile-sidenav-wrapper
    %button.closeSideNav.sidebars-close-btn
      %i.fa-solid.fa-xmark
    .flex-shrink-0.bg-white.menu-list-wrapper
      .user-action-btn-wrap
        .flex-container
          .flex-item-1
            =image_tag('mobile_nav_placeholder.png', alt: 'mobile_nav_placeholder', class: 'user-img')
          .flex-item-2
            - if account_signed_in?
              -if current_account.present? && current_account.email.present?
                %span.txt= current_account.email.split('@').first.try(:titleize)
              %div
                = link_to destroy_account_session_path, method: :delete do
                  %button.act-btn.login-btn logout
            - else
              %span.txt Login to continue
              %div
                = link_to '/accounts/sign_in' do
                  %button.act-btn.login-btn login
                = link_to '/accounts/sign_up' do
                  %button.act-btn.transparent.register-btn register

      %ul.list-unstyled

        -@menu_list.each do |menu|
          - menu_columns = menu.menu_columns
          - menu_columns = menu_columns.sort_by(&:position)

          %li.menu-list-item
            %button.btn.btn-toggle.m-toggle-btn.collapsed{"aria-expanded" => "false", "data-bs-target" => "#menu_#{menu.id}", "data-bs-toggle" => "collapse"}
              = menu.title

          .collapse{id: "menu_#{menu.id}"}
            - menu_columns.each do |menu_column|
              - menu_items = menu_column.menu_items
              %ul.btn-toggle-nav.list-unstyled
                %li
                  %a.a-link{:href => menu_column.link}
                    %h6= menu_column.title
                    - menu_items.each do |menu_item|
                      %li
                        %a.menu-submenu.a-link{:href=> menu_item.link}
                          = menu_item.title
- if params[:kind].present?
  - if params[:kind] == "b1g1" && (bmgnx_hash = PromotionPipeLine.bmgnx_hash).present?
    - product_title = Promotion.bmgnx_offer_message(bmgnx_hash)
  - elsif params[:facets].present?
    - if params[:facets].index('colour') == 0 && @category_title.present?
      - product_title = params[:facets].partition('colour-').last.split('--').join(' ').titleize
      - product_title = "#{product_title} #{@category_title}"
    - else
      - product_title = params[:facets].titleize
  - elsif @category_title.present?
    - product_title = "#{@category_title.strip.camelize}"
  - else
    - product_title = "#{params[:kind].gsub(/-/,' ').humanize}"
- elsif params[:term].present? && params[:utf8]
  - product_title = "Search results for #{params[:term].humanize}"
- elsif params[:q].present? && params[:utf8].blank?
  - product_title = params[:q].humanize
- elsif params[:collection].present?
  - product_title = params[:collection].titleize
- elsif params[:id].present?
  - product_title = params[:id].titleize
-if @store_page.present?
  %main#mainContentContainer.main-content-container
    %span#mobileMenuOverlay
    .catalog-page-container
      .container-layout-max
        .main-flex-container.plp-page-luxe
          .flex--item-1.sticky-element.flex--item-11
            %div
              .breadcrumb-wrapper
                - if @breadcrumbs.present?
                  - final = @breadcrumbs.pop
                  %ul.breadcrumb-list{itemscope: '', itemtype: 'http://schema.org/BreadcrumbList'}
                    - @breadcrumbs.each_with_index do |crumb, i|
                      %li.b-item{itemscope: '', itemprop: 'itemListElement', itemtype: 'http://schema.org/ListItem'}
                        = link_to crumb[:url], itemprop: 'item', 'aria-label' => 'Breadcrumb url', class: 'b-link' do
                          %span.bcrumb{itemprop: 'name'}= crumb[:title]
                        %meta{itemprop: 'position', content: i+1}
                    %li.b-item{itemscope: '', itemprop: 'itemListElement', itemtype: 'http://schema.org/ListItem'}
                      - url = final[:url].present? ? final[:url] : request.url.split('?').first
                      %a.b-link{href: url, itemprop: 'item', 'aria-label' => 'Breadcrumb url'}
                      %span.final.bcrumb{itemprop: 'name'}= final[:title]
                      %meta{itemprop: 'position', content: @breadcrumbs.length+1}
              / .ready-ship-wrapper
              /   %label.common-checkbox-grp
              /     Ready to ship
              /     %input{:checked => "checked", :type => "checkbox"}/
              /     .control-indicator
              - index = request.path.index(params[:facets]) - 2 if params[:facets].present?
              - page_base_url = index ? request.path[0..index] : request.path
              - facets_info = FACETED_URL_KINDS[params[:kind]]
              - if facets_info
                - url_params = create_url_parameters(@facet_properties, params)
              - rating_price = RATING_ENABLE ? ['rating','price'] : ['price']
              .filter-accordion-wrapper
                - if !browser.device.mobile? && @store_page['filters']['id_filters'].present?
                  - @store_page['filters']['id_filters'].each.with_index(1) do |id_filter,index|
                    - if allow_facet_for_view?(params[:kind], id_filter['name'], id_filter['priority'])
                      - filter_name = id_filter['name'].try(:downcase).to_s.gsub(' ', '-')
                      .accordion-item
                        %b.accordion-header{id: "accordionFilter#{filter_name}"}
                          %button.accordion-button.collapsed.filter-header-title{"aria-controls" => "accordionFilter#{filter_name}Collapse", "aria-expanded" => "true", "data-bs-target" => "#accordionFilter#{filter_name}Collapse", "data-bs-toggle" => "collapse", :type => "button"}
                            //%a.button.tiny.tab-filter-fix{href: "#tab_check_#{index}", id: "#{id_filter['name'].parameterize}"}
                            = id_filter['name']
                            - if false #(filter_keys = params[id_filter['key']]).present?
                              - active_filter = get_active_filters_count(id_filter['list'].map{|x| x['value']}, filter_keys)
                              - if active_filter > 0
                                %span.tiny-green= "#{active_filter}"

                        .accordion-collapse.collapse{"aria-labelledby" => "accordionFilter#{filter_name}", "data-bs-parent" => "#accordionExample",id: "accordionFilter#{filter_name}Collapse"}
                          .accordion-body.filterCustomScrollbar
                            - if id_filter['list'].present?
                              - data_attr = { property: id_filter['name'].downcase }
                              - id_filter['list'].each do |list|
                                - box_type = id_filter['key'] == 'property_value_ids' && facets_info && !id_filter['name'].match(/color/i).present? ? 'radio' : 'checkbox'
                                - if facets_info
                                  - current_prop = { name: list['pv_name'], priority: id_filter['priority'], type: box_type }
                                  - data_attr.merge!(current_prop)
                                  - if id_filter['priority'].to_i > 0 && create_url_for_facets?(box_type, @facet_properties)
                                    - current_prop[:property] = id_filter['prop_name']
                                    - link = create_faceted_url(params[:kind], current_prop, @facet_properties, page_base_url) + url_params.to_s
                                - data_attr[:key] = "#{id_filter['key']}[]"
                                - if id_filter['name'].match(/color/i).present?
                                  - color_val = (list['color_code'] == "-1") ? "null" : "rgba(#{list['color_code']})"
                                  .single_line_div
                                    - if params[id_filter['key']].present? && params[id_filter['key']].split(',').include?("#{list['value']}")
                                      - data_attr[:chip] = "#{id_filter['name']} - #{list['name']}"
                                      %a.facet-link-desktop{href: link}
                                        .on-off-checkbox.color-switch
                                          %input.filter-select.color-input{name: "#{id_filter['key']}-#{id_filter['name']}[]" ,id: "#{id_filter['key']}_#{list['value']}" ,type: 'checkbox', placeholder: "#{list['name']}".downcase.tr(' ','-'), value: "#{list['value']}", checked: 'checked', class: [('color-append' if ['saree-color','kameez-color','color', 'lehenga-color'].include?(id_filter['name'].parameterize)),("#{id_filter['name'].parameterize}")], data: data_attr }
                                          %label.label-custom-color{for: "#{id_filter['key']}_#{list['value']}", class: ('multicolor-value' if list['color_code'] == "-1"), style: "background: #{color_val}"}
                                        %label.label-custom{for: "#{id_filter['key']}_#{list['value']}", class: ('label-desktop-fix' unless browser.device.mobile?)}
                                          = list['name']
                                          - if false #list['count'] > 0
                                            %span.round.success.label= list['count']
                                    - else
                                      %a.facet-link-desktop{href: link}
                                        .on-off-checkbox.color-switch
                                          %input.filter-select.color-input{name: "#{id_filter['key']}-#{id_filter['name']}[]" ,id: "#{id_filter['key']}_#{list['value']}" ,type: 'checkbox', placeholder: "#{list['name']}".downcase.tr(' ','-'), data: data_attr, value: "#{list['value']}", class: [("color-append" if ['saree-color','kameez-color','color', 'lehenga-color'].include?(id_filter['name'].parameterize)),("#{id_filter['name'].parameterize}")]}
                                          %label.label-custom-color{for: "#{id_filter['key']}_#{list['value']}", class: ('multicolor-value' if list['color_code'] == "-1"), style: "background: #{color_val}"}
                                        %label.label-custom{for: "#{id_filter['key']}_#{list['value']}", class: ('label-desktop-fix' unless browser.device.mobile?)}
                                          = list['name']
                                          - if false #list['count'] > 0
                                            %span.round.warning.label= list['count']
                                - else
                                  - class_type = box_type == 'radio' ? 'on-off-radiobox' : 'on-off-checkbox'
                                  .single_line_div{style: "margin-bottom: 0.5rem;"}
                                    - if params[id_filter['key']].present? && params[id_filter['key']].split(',').include?("#{list['value']}")
                                      - data_attr[:chip] = "#{id_filter['name']} - #{list['name']}"
                                      %a.facet-link-desktop{href: link}
                                        .switch.tiny{class: class_type}
                                          %input.filter-select{name: "#{id_filter['key']}-#{id_filter['name']}[]" ,id: "#{id_filter['key']}_#{list['value']}" ,class: "#{id_filter['name'].parameterize}" ,type: box_type, value: "#{list['value']}", checked: 'checked', data: data_attr}
                                          %label{for: "#{id_filter['key']}_#{list['value']}"}
                                        %label.label-custom{for: "#{id_filter['key']}_#{list['value']}", class: ('label-desktop-fix' unless browser.device.mobile?)}
                                          = list['name']
                                          - if false #list['count'] > 0
                                            %span.round.success.label= list['count']
                                    - else
                                      %a.facet-link-desktop{href: link}
                                        .switch.tiny{class: class_type}
                                          %input.filter-select{name: "#{id_filter['key']}-#{id_filter['name']}[]" ,id: "#{id_filter['key']}_#{list['value']}" ,class: "#{id_filter['name'].parameterize}" ,type: box_type, value: "#{list['value']}", data: data_attr}
                                          %label{for: "#{id_filter['key']}_#{list['value']}"}
                                        %label.label-custom{for: "#{id_filter['key']}_#{list['value']}", class: ('label-desktop-fix' unless browser.device.mobile?)}
                                          = list['name']
                                          - if false #list['count'] > 0
                                            %span.round.warning.label= list['count']

                / .accordion-item
                /   %b#accordionFilterCategory.accordion-header
                /     %button.accordion-button.filter-header-title{"aria-controls" => "accordionFilterCategoryCollapse", "aria-expanded" => "true", "data-bs-target" => "#accordionFilterCategoryCollapse", "data-bs-toggle" => "collapse", :type => "button"}
                /       Category
                /       %span.active-dot
                /       %span.clear-btn Clear
                /   #accordionFilterCategoryCollapse.accordion-collapse.collapse.show{"aria-labelledby" => "accordionFilterCategory", "data-bs-parent" => "#accordionExample"}
                /     .accordion-body
                /       .filter-search
                /         %input.search-box.common-search-box{:placeholder => "Search category", :type => "text"}/
                /       .checkbox-list.filterCustomScrollbar
                /         .checkbox-item
                /           %label.common-checkbox-grp
                /             %span.l-label Kurtis
                /             %input{:checked => "checked", :type => "checkbox"}/
                /             .control-indicator
                /         .checkbox-item
                /           %label.common-checkbox-grp
                /             %span.l-label Jewelleries
                /             %input{:type => "checkbox"}/
                /             .control-indicator
                /         .checkbox-item
                /           %label.common-checkbox-grp
                /             %span.l-label Ethnic Kurtis
                /             %input{:type => "checkbox"}/
                /             .control-indicator
                /         .checkbox-item
                /           %label.common-checkbox-grp
                /             %span.l-label Kurtis
                /             %input{:checked => "checked", :type => "checkbox"}/
                /             .control-indicator
                /         .checkbox-item
                /           %label.common-checkbox-grp
                /             %span.l-label Kurtis
                /             %input{:checked => "checked", :type => "checkbox"}/
                /             .control-indicator
                /         .checkbox-item
                /           %label.common-checkbox-grp
                /             %span.l-label Jewelleries
                /             %input{:type => "checkbox"}/
                /             .control-indicator
                /         .checkbox-item
                /           %label.common-checkbox-grp
                /             %span.l-label Ethnic Kurtis
                /             %input{:type => "checkbox"}/
                /             .control-indicator
                /         .checkbox-item
                /           %label.common-checkbox-grp
                /             %span.l-label Kurtis
                /             %input{:checked => "checked", :type => "checkbox"}/
                /             .control-indicator
                /         .checkbox-item
                /           %label.common-checkbox-grp
                /             %span.l-label Kurtis
                /             %input{:checked => "checked", :type => "checkbox"}/
                /             .control-indicator
                /         .checkbox-item
                /           %label.common-checkbox-grp
                /             %span.l-label Jewelleries
                /             %input{:type => "checkbox"}/
                /             .control-indicator
                /         .checkbox-item
                /           %label.common-checkbox-grp
                /             %span.l-label Ethnic Kurtis
                /             %input{:type => "checkbox"}/
                /             .control-indicator
                /         .checkbox-item
                /           %label.common-checkbox-grp
                /             %span.l-label Kurtis
                /             %input{:checked => "checked", :type => "checkbox"}/
                /             .control-indicator
                / .accordion-item
                /   %b#accordionFilterDesigner.accordion-header
                /     %button.accordion-button.collapsed.filter-header-title{"aria-controls" => "accordionFilterDesignerCollapse", "aria-expanded" => "true", "data-bs-target" => "#accordionFilterDesignerCollapse", "data-bs-toggle" => "collapse", :type => "button"}
                /       Designer
                /       / <span class="active-dot"></span> <span class="clear-btn">Clear</span>
                /   #accordionFilterDesignerCollapse.accordion-collapse.collapse{"aria-labelledby" => "accordionFilterDesigner", "data-bs-parent" => "#accordionExample"}
                /     .accordion-body
                /       .filter-search
                /         %input.search-box.common-search-box{:placeholder => "Search designer", :type => "text"}/
                /       .checkbox-list.filterCustomScrollbar
                /         .checkbox-item
                /           %label.common-checkbox-grp
                /             %span.l-label Sangam Prints
                /             %input{:checked => "checked", :type => "checkbox"}/
                /             .control-indicator
                /         .checkbox-item
                /           %label.common-checkbox-grp
                /             %span.l-label Maniyar
                /             %input{:type => "checkbox"}/
                /             .control-indicator
                /         .checkbox-item
                /           %label.common-checkbox-grp
                /             %span.l-label Kimisha
                /             %input{:type => "checkbox"}/
                /             .control-indicator
                / .accordion-item
                /   %b#accordionFilterCollection.accordion-header
                /     %button.accordion-button.collapsed.filter-header-title{"aria-controls" => "accordionFilterCollectionCollapse", "aria-expanded" => "true", "data-bs-target" => "#accordionFilterCollectionCollapse", "data-bs-toggle" => "collapse", :type => "button"}
                /       Collection
                /       / <span class="active-dot"></span> <span class="clear-btn">Clear</span>
                /   #accordionFilterCollectionCollapse.accordion-collapse.collapse{"aria-labelledby" => "accordionFilterCollection", "data-bs-parent" => "#accordionExample"}
                /     .accordion-body
                /       .filter-search
                /         %input.search-box.common-search-box{:placeholder => "Search Collection", :type => "text"}/
                /       .checkbox-list.filterCustomScrollbar
                /         .checkbox-item
                /           %label.common-checkbox-grp
                /             %span.l-label Kurtis
                /             %input{:checked => "checked", :type => "checkbox"}/
                /             .control-indicator
                /         .checkbox-item
                /           %label.common-checkbox-grp
                /             %span.l-label Jewelleries
                /             %input{:type => "checkbox"}/
                /             .control-indicator
                / .accordion-item
                /   %b#accordionFilterSize.accordion-header
                /     %button.accordion-button.collapsed.filter-header-title{"aria-controls" => "accordionFilterSizeCollapse", "aria-expanded" => "true", "data-bs-target" => "#accordionFilterSizeCollapse", "data-bs-toggle" => "collapse", :type => "button"}
                /       Size
                /       / <span class="active-dot"></span> <span class="clear-btn">Clear</span>
                /   #accordionFilterSizeCollapse.accordion-collapse.collapse{"aria-labelledby" => "accordionFilterSize", "data-bs-parent" => "#accordionExample"}
                /     .accordion-body
                /       .checkbox-list.filterCustomScrollbar
                /         .checkbox-item
                /           %label.common-checkbox-grp
                /             %span.l-label XS
                /             %input{:checked => "checked", :type => "checkbox"}/
                /             .control-indicator
                /         .checkbox-item
                /           %label.common-checkbox-grp
                /             %span.l-label S
                /             %input{:type => "checkbox"}/
                /             .control-indicator
                /         .checkbox-item
                /           %label.common-checkbox-grp
                /             %span.l-label M
                /             %input{:type => "checkbox"}/
                /             .control-indicator
                / .accordion-item
                /   %b#accordionFilterDeliveryTime.accordion-header
                /     %button.accordion-button.collapsed.filter-header-title{"aria-controls" => "accordionFilterDeliveryTimeCollapse", "aria-expanded" => "true", "data-bs-target" => "#accordionFilterDeliveryTimeCollapse", "data-bs-toggle" => "collapse", :type => "button"}
                /       Delivery Time
                /       / <span class="active-dot"></span> <span class="clear-btn">Clear</span>
                /   #accordionFilterDeliveryTimeCollapse.accordion-collapse.collapse{"aria-labelledby" => "accordionFilterDeliveryTime", "data-bs-parent" => "#accordionExample"}
                /     .accordion-body
                /       .radio-list.filterCustomScrollbar
                /         .radio-item
                /           .common-radio-btn-grp
                /             %input#delivery-radio-1{:checked => "checked", :name => "delivery-radio", :type => "radio"}/
                /             %label.radio-label{:for => "delivery-radio-1"} 1 Week
                /         .radio-item
                /           .common-radio-btn-grp
                /             %input#delivery-radio-2{:name => "delivery-radio", :type => "radio"}/
                /             %label.radio-label{:for => "delivery-radio-2"} 2 Week
                / .accordion-item
                /   %b#accordionFilterColor.accordion-header
                /     %button.accordion-button.collapsed.filter-header-title{"aria-controls" => "accordionFilterColorCollapse", "aria-expanded" => "true", "data-bs-target" => "#accordionFilterColorCollapse", "data-bs-toggle" => "collapse", :type => "button"}
                /       Color
                /       / <span class="active-dot"></span> <span class="clear-btn">Clear</span>
                /   #accordionFilterColorCollapse.accordion-collapse.collapse{"aria-labelledby" => "accordionFilterColor", "data-bs-parent" => "#accordionExample"}
                /     .accordion-body
                /       .filter-search
                /         %input.search-box.common-search-box{:placeholder => "Search Color", :type => "text"}/
                /       .checkbox-list.filterCustomScrollbar
                /         .checkbox-item
                /           %label.common-checkbox-grp
                /             %span.colour-label{:style => "background-color: rgb(211, 75, 86)"}
                /             %span.l-label red
                /             %input{:checked => "checked", :type => "checkbox"}/
                /             .control-indicator
                /         .checkbox-item
                /           %label.common-checkbox-grp
                /             %span.colour-label{:style => "background-color: rgb(94, 177, 96)"}
                /             %span.l-label green
                /             %input{:type => "checkbox"}/
                /             .control-indicator
                - if !browser.device.mobile? && @store_page['filters']['range_filters'].present?
                  - @store_page['filters']['range_filters'].each.with_index(1) do |range_filter,range_index|
                    - filter_name = range_filter['name'].try(:downcase).to_s
                    - filter_name = 'price_range' if filter_name == 'price'
                    - data_attr = { property: range_filter['name'].downcase }
                    - if rating_price.include?(range_filter['name'].try(:downcase))
                      .accordion-item
                        %b.accordion-header{id: "accordionFilter#{filter_name}"}
                          %button.accordion-button.collapsed.filter-header-title{"aria-controls" => "accordionFilter#{filter_name}Collapse", "aria-expanded" => "true", "data-bs-target" => "#accordionFilter#{filter_name}Collapse", "data-bs-toggle" => "collapse", :type => "button"}
                            = range_filter['name']
                            / <span class="active-dot"></span> <span class="clear-btn">Clear</span>
                        .accordion-collapse.collapse{"aria-labelledby" => "accordionFilter#{filter_name}", "data-bs-parent" => "#accordionExample",id: "accordionFilter#{filter_name}Collapse"}
                          .accordion-body.filterCustomScrollbar
                            - if params[range_filter['keys']['min']].present? && params[range_filter['keys']['max']].present?
                              %input{id: "#{range_filter['keys']['min']}" ,class: 'range_filter_input' ,type: 'hidden' ,name: "#{range_filter['keys']['min']}", value: params[range_filter['keys']['min']]}
                              %input{id: "#{range_filter['keys']['max']}" ,class: 'range_filter_input' ,type: 'hidden' ,name: "#{range_filter['keys']['max']}", value: params[range_filter['keys']['max']]}
                            - else
                              %input{id: "#{range_filter['keys']['min']}" ,class: 'range_filter_input' ,type: 'hidden' ,name: "#{range_filter['keys']['min']}"}
                              %input{id: "#{range_filter['keys']['max']}" ,class: 'range_filter_input' ,type: 'hidden' ,name: "#{range_filter['keys']['max']}"}
                            - if range_filter['list'].present?
                              - range_filter['list'].each do |list|
                                .single_line_div{style: "margin-bottom: 0.5rem;"}
                                  - if params[range_filter['keys']['min']].to_i == list['values']['min'].to_i && params[range_filter['keys']['max']].to_i == list['values']['max'].to_i
                                    %a.facet-link-desktop{href: '#'}
                                      .on-off-radiobox.switch.tiny
                                        %input.filter-select{name: "#{range_filter['name']}_range" ,value: '' ,type: 'radio' ,id: "#{range_filter['name'].parameterize}_#{list['values']['min'].to_i}-#{list['values']['max'].to_i}", class: "#{range_filter['name'].parameterize}" ,checked: 'checked', data: {min_input: "#{range_filter['keys']['min']}" ,max_input: "#{range_filter['keys']['max']}",max: "#{list['values']['max'].to_i}" ,min: "#{list['values']['min'].to_i}" ,chip: "#{range_filter['name']} - #{list['name']}"}.merge(data_attr)}
                                        %label{for: "#{range_filter['name'].parameterize}_#{list['values']['min'].to_i}-#{list['values']['max'].to_i}"}
                                      %label.label-custom{for: "#{range_filter['name'].parameterize}_#{list['values']['min'].to_i}-#{list['values']['max'].to_i}", class: ('label-desktop-fix' unless browser.device.mobile?)}
                                        - if range_filter['name'].try(:downcase) == 'price'
                                          = "#{get_symbol_from(@hex_symbol)}#{list['name']}"
                                          - if false #list['count'] > 0
                                            %span.round.success.label= list['count']
                                        - else
                                          = list['name']
                                          - if false #list['count'] > 0
                                            %span.round.success.label= list['count']
                                  - else
                                    %a.facet-link-desktop{href: '#'}
                                      .on-off-radiobox.switch.tiny
                                        %input.filter-select{name: "#{range_filter['name']}_range" ,value: '' ,type: 'radio' ,id: "#{range_filter['name'].parameterize}_#{list['values']['min'].to_i}-#{list['values']['max'].to_i}", class: "#{range_filter['name'].parameterize}" ,data: {min_input: "#{range_filter['keys']['min']}" ,max_input: "#{range_filter['keys']['max']}",max: "#{list['values']['max'].to_i}" ,min: "#{list['values']['min'].to_i}"}.merge(data_attr)}
                                        %label{for: "#{range_filter['name'].parameterize}_#{list['values']['min'].to_i}-#{list['values']['max'].to_i}"}
                                      %label.label-custom{for: "#{range_filter['name'].parameterize}_#{list['values']['min'].to_i}-#{list['values']['max'].to_i}", class: ('label-desktop-fix' unless browser.device.mobile?)}
                                        - if range_filter['name'].try(:downcase) == 'price'
                                          = "#{get_symbol_from(@hex_symbol)}#{list['name']}"
                                          //%span.round.warning.label= list['count']
                                        - else
                                          = list['name']
                                          //%span.round.warning.label= list['count']



          .flex--item-2.flex--item-22
            .home-page-container.plp-page
              -if @luxe_story.present?
                = render partial: 'luxe/pages/category_widgets'
            .container-fluid
              .row
                .col-12
                  .heading-sort-wrapper
                    .heading-wrap
                      %h1.category-heading
                        =product_title
                      %span.item-count="#{@store_page['results']} Styles"

            
            -if @seo.present? && @seo.top_content.present?
              .msg-box1
                #seo_content=raw @seo.top_content
            .select_box.sort-by-wrap
              %span.sort-by-txt SORT BY
              #sortByDropdownMenuBox.dropdown
                - if @country_code == 'IN' && params.has_key?(:q)
                  - select_default = ['HandPicked']
                  = select_tag 'sort', options_for_select( ApplicationHelper::UNBXD_SORT, params[:sort] || select_default), class: 'form_input_select dsort', 'aria-label'=> 'sort input'
                - elsif @country_code == 'IN'
                  = select_tag 'sort', options_for_select(ApplicationHelper::SORT_TYPE.to_a, ApplicationHelper::SORT_TYPE[Api::V1::StoreHelper.domestic_sorts[(params[:sort].presence || 7).to_s][:name]]), class: 'form_input_select dsort', 'aria-label'=> 'sort input'
                - else
                  = select_tag 'sort', options_for_select(ApplicationHelper::SORT_TYPE.to_a, ApplicationHelper::SORT_TYPE[Api::V1::StoreHelper.international_sorts[(params[:sort].presence || 7).to_s][:name]]), class: 'form_input_select dsort', 'aria-label'=> 'sort input'
            %span{:itemprop => "WebPage", :itemscope => "", :itemtype => "http://schema.org/WebPage"}
              - base_url = request.url.split('?').first
              %span{:itemprop => "url", :content => base_url}
              %span{:itemprop => "mainEntity", :itemscope => "", :itemtype => "http://schema.org/OfferCatalog"}
                -if product_title ||= false
                  %span{:itemprop => "name", :content => product_title}
                %span{:itemprop => "url", :content => base_url}
                %span{:itemprop => "numberOfItems", :content => @store_page['designs'].count}

                .product-list-wrapper
                  .container-fluid.xs-no-padding
                    .row.xs-no-padding.xs-no-margin
                      -@store_page['designs'].each_with_index do |design,index|
                        - image_src = design['sizes']['original']
                        - image_src_webp = design['sizes']['long_webp'].to_s.gsub('.jpg', '.webp').gsub('.JPG', '.webp').gsub('.jpeg', '.webp').gsub('.JPEG', '.webp')
                        .col-6.col-sm-4.col-md-4.col-lg-3.col-xl-3.col-xxl-3.no-padding-horizontal.product-card-col{:itemprop => "itemListElement", :itemscope => "", :itemtype => "http://schema.org/Product"}
                          %a.common-product-card-wrapper{:href => "/#{design['brand']}/buy/#{design['title'].parameterize}/#{design['id']}"}
                            - hash = @category.breadcrumb_path if @category.present?
                            - accurate_category_id = @accurate_category_id
                            - accurate_category_title = @accurate_category_title
                            - crumbs_category = hash.keys if hash.present?
                            - item_category = crumbs_category[1] if crumbs_category.present?
                            - item_category2 = crumbs_category[2] if crumbs_category.present?
                            - item_category3 = crumbs_category[3] if crumbs_category.present?
                            - item_category4 = crumbs_category[4] if crumbs_category.present?
                            - item_category5 = crumbs_category[5] if crumbs_category.present?
                            - array_index = crumbs_category.length > 2 ? 2 : 1 if crumbs_category.present?
                            - title = crumbs_category[array_index] if array_index.present? && crumbs_category.present?
                            - cc = CurrencyConvert.find_by(country_code: @country_code)
                            - market_rate = cc.present? ? cc.market_rate : 1
                            - main_price = (design['discount_price'] * market_rate).round(2)
                            - discount = ((design['price'] - design['discount_price'] )* market_rate).round(2)
                            - ga_data = {id: design['id'].to_s, index: index, breadcrumb_path: crumbs_category, category_name: accurate_category_title, category_id: accurate_category_id,item_category: item_category,item_category2: item_category2, item_category3: item_category3,item_category4: item_category4,item_category5:item_category5,brand: design['designer'],color: design['color'], title: design['title'], price: main_price, discount: discount}.to_json.html_safe
                            .p-img-box.plp-img-box.design-block{data: {gavalue: ga_data}}
                              %span{:itemprop => "image", :content => IMAGE_PROTOCOL + image_src}
                              -if image_src_webp.size > 1
                                %picture
                                  %source.cmn-img-fluid.p-img{:srcset => IMAGE_PROTOCOL + image_src_webp, :type => "image/webp", :alt => "#{design['title']}"}
                                  %img.cmn-img-fluid.p-img{src: IMAGE_PROTOCOL + image_src_webp, :alt => "#{design['title']}", data: {src: IMAGE_PROTOCOL + image_src_webp, index: index}}
                              -else
                                %img.cmn-img-fluid.p-img{:alt => "#{design['title']}", src: IMAGE_PROTOCOL + image_src}/
                              -if (1..5).include?(design['eta'])
                                .ready_to_ship_div
                                  =image_tag('thunder-2.png', class: 'img-responsive image_dimension thunder_image',height: 30,width: 30)
                                  Ready To Ship
                              / .p-overlay.nestedClickStopPropagation
                              /   .p-txt-box
                              /     %span.p-quick-txt{"data-bs-target" => "#quickViewProductModal", "data-bs-toggle" => "modal"}
                              /       quick view
                              /       %i.mg-l.fa-solid.fa-angle-right
                              /   .p-size-box
                              /     %button.p-size-btn.size-selected xs
                              /     %button.p-size-btn s
                              /     %button.p-size-btn m
                              /     %button.p-size-btn l
                              /     %button.p-size-btn xl
                              /     %button.p-size-btn xxl
                              /   .p-share-cart-box
                              /     //= link_to "#{design['design_path']}" do
                              /     %button.p-add-cart-btn{:href => "#{design['design_path']}"} add to cart
                              /     %button.p-wishlist-btn.p-wishlisted
                              /       / prettier-ignore
                              /       %svg#icon_wishlist_inactive{"data-name" => "icon/wishlist/inactive", :height => "18", :viewbox => "0 0 18 18", :width => "18", :xmlns => "http://www.w3.org/2000/svg"}
                              /         %rect#Rectangle_23416{"data-name" => "Rectangle 23416", :fill => "#fff", :height => "18", :opacity => "0", :width => "18"}
                              /         %path#Path_197893{:d => "M2.2,7.848a4.835,4.835,0,0,0,1.443,3.634c1.155,1.14,4.979,3.706,5.124,3.848a1.035,1.035,0,0,0,.433.143,1.035,1.035,0,0,0,.433-.143,53.653,53.653,0,0,0,5.124-3.848A4.835,4.835,0,0,0,16.2,7.848,3.8,3.8,0,0,0,9.272,5.639,3.9,3.9,0,0,0,2.2,7.848Z", "data-name" => "Path 197893", :fill => "none", :stroke => "#1f1f1f", "stroke-linecap" => "round", "stroke-linejoin" => "round", "stroke-width" => "1", :transform => "translate(-0.2 -0.737)"}
                              /     %button.p-share-btn
                              /       / prettier-ignore
                              /       %svg#icon_share{"data-name" => "icon/share", :height => "18", :viewbox => "0 0 18 18", :width => "18", :xmlns => "http://www.w3.org/2000/svg"}
                              /         %g#Rectangle_21663{"data-name" => "Rectangle 21663", :opacity => "0", :stroke => "#f5f5f5", "stroke-width" => "1"}
                              /           %rect{:height => "18", :stroke => "none", :width => "18"}
                              /           %rect{:fill => "none", :height => "17", :width => "17", :x => "0.5", :y => "0.5"}
                              /         %path#Path_200152{:d => "M13.3,12.38a1.928,1.928,0,0,0-1.346.568L7.06,9.888a2.581,2.581,0,0,0,.062-.516,2.581,2.581,0,0,0-.062-.516L11.9,5.826a1.979,1.979,0,0,0,1.4.6,2.137,2.137,0,0,0,2.061-2.212A2.137,2.137,0,0,0,13.3,2a2.137,2.137,0,0,0-2.061,2.212,2.581,2.581,0,0,0,.062.516L6.462,7.757a1.979,1.979,0,0,0-1.4-.6A2.137,2.137,0,0,0,3,9.372a2.137,2.137,0,0,0,2.061,2.212,1.979,1.979,0,0,0,1.4-.6l4.891,3.067a2.224,2.224,0,0,0-.055.479A2.086,2.086,0,0,0,13.3,16.685a2.158,2.158,0,0,0,0-4.305Z", "data-name" => "Path 200152", :fill => "#1f1f1f", :transform => "translate(-0.183 -0.906)"}
                              / %span.p-label.black Ready to ship
                              / %span.p-label.red Best seller
                            .p-detail-box
                              %span.p-title= design['designer'].titleize
                              %span{:itemprop => "name"}
                                %span.p-des= design['title'].titleize
                              %span.p-amount-box
                                %span.p-price
                                  / %i.currency-icon.fa-solid
                                  ="#{get_symbol_from(@hex_symbol)}#{number_with_delimiter(design['discount_price'])}"
                                -if design['discount_percent'].to_f > 0
                                  %span.p-mrp
                                    / %i.currency-icon.fa-solid
                                    ="#{get_symbol_from(@hex_symbol)}#{design['price']}"
                                  %span.p-discount="(#{design['discount_percent']}" + "% OFF)"


                          %span{:itemprop => "offers", :itemscope => "", :itemtype => "http://schema.org/Offer"}
                            %span{:itemprop => "url", :content => "/#{design['brand']}/buy/#{design['title'].parameterize}/#{design['id']}"}
                            %span{:itemprop => "priceCurrency", :content => schema_symbol(@symbol)}
                            %span{:itemprop => "price", :content => design['discount_price']}
                            - if design['state'] == 'in_stock'
                              %span{:itemprop => "availability", :content => "http://schema.org/InStock", :itemtype => "http://schema.org/ItemAvailability"}
                            - else
                              %span{:itemprop => "availability", :content => "http://schema.org/OutOfStock", :itemtype => "http://schema.org/ItemAvailability"}
                          -if false
                            %span{:itemprop => "priceCurrency", :content => schema_symbol(@symbol)}
                            %span{:itemprop => "price", :content => design['discount_price']}
                            - if design['state'] == 'in_stock'
                              %span{:itemprop => "availability", :content => "http://schema.org/InStock", :itemtype => "http://schema.org/ItemAvailability"}
                            - else
                              %span{:itemprop => "availability", :content => "http://schema.org/OutOfStock", :itemtype => "http://schema.org/ItemAvailability"}
                .current_page{data: {page: (params[:page] || 1)}}
                .navigate-page.text-center.li_append
                  - if @store_page['previous_page'].present?
                    -if @store_page['previous_page'].to_i == 1 && params[:q].present?
                      %a#previous-link.button.secondary.nav-button.tiny.previous.prev{href: get_next_prev_url(request.fullpath,@store_page['previous_page']), rel: "prev"} &laquo; Prev
                    -elsif @store_page['previous_page'].to_i == 1
                      %a#previous-link.button.secondary.nav-button.tiny.previous.prev{href: request.path, rel: "prev"} &laquo; Prev
                    -else
                      %a#previous-link.button.secondary.nav-button.tiny.previous.prev{href: get_next_prev_url(request.fullpath,@store_page['previous_page']), rel: "prev"} &laquo; Prev
                  - if @store_page['next_page'].present?
                    %a#next-link.button.nav-button.tiny.next{href: get_next_prev_url(request.fullpath,@store_page['next_page']), rel: "next"} Next &raquo;

                -unless params[:page].present?
                  =render partial: 'luxe/store/seo_post'

                =render partial: 'luxe/store/seo_popular_search' if false #@popular_links.present?

                / .product-coupon-code-wrapper
                /   .container-fluid
                /     .row
                /       .col-12
                /         .single-img-banner-wrapper
                /           .bg-layer
                /             %img.cmn-img-fluid.black-bg-layer{:alt => "Mirraw", :src => "../../assets/img/catalog/luxe_code_banner_bg.png"}/
                /             .img-txt-overlay
                /               .container-fluid
                /                 .row.align-items-center
                /                   .col-6
                /                     %span.info-txt Use LUXE22 to avail 60% off on luxury range
                /                     %button.btn-white.code-btn copy code
                /                   .col-6
                /                     %img.cmn-img-fluid.banner-img{:alt => "Mirraw", :src => "../../assets/img/catalog/luxe_code_banner_bag.png"}/
                .sort-by-popup-cover-black.sortByPopUpCoverBlack
                .sort-filter-option-wrapper.sortByOptionsBox
                  .radio-list
                    - ApplicationHelper::SORT_TYPE.to_a.each_with_index do |item, i|
                      .radio-item.radio-sortby
                        .common-radio-btn-grp.hideSortByOptions
                          - sort_id = "sort_radio_#{i+1}"
                          %input{id: sort_id,:name => "sortopt", :type => "radio", :value => item[1], label: item[0]}
                          / %input#sort-radio1{:checked => "checked", :name => "sort-radio", :type => "radio", value: item[1]}/
                          %label.radio-label.text-uppercase{:for => sort_id}=item[0]
                .sort-filter-drawer-wrapper
                  .flex-container
                    .mobile-message-block.offer-txt.mobile.polp
                      - if @offer_message.present?
                        =raw @offer_message
                      - else
                        %p
                          =raw "For Personal Assistance, you can WhatsApp us on"
                          = link_to "+91 9137407527. ",'https://api.whatsapp.com/send?phone=919137407527&text=Hello', target: '_blank', style: 'color:#f56a6a;'
                      = render partial: 'luxe/pages/deal_timer_mobile'
                    .flex-filter
                      .flex-item.divider
                        %button.action-btn.showSortByOptions
                          / prettier-ignore
                          %svg#icon_sort_by.svg--icon{"data-name" => "icon/sort by", :height => "24", :viewbox => "0 0 24 24", :width => "24", :xmlns => "http://www.w3.org/2000/svg"}
                            %g#Group_542{"data-name" => "Group 542", :transform => "translate(5.75 5.75)"}
                              %path#Path_754{:d => "M-6195.681-2094.888a.75.75,0,0,1-.75-.75v-10l-2.765,2.443a.75.75,0,0,1-1.059-.065.75.75,0,0,1,.065-1.059l4.011-3.544a.751.751,0,0,1,.8-.122.75.75,0,0,1,.442.684v11.667A.75.75,0,0,1-6195.681-2094.888Z", "data-name" => "Path 754", :fill => "#1f1f1f", :transform => "translate(6199.692 2107.305)"}
                              %path#Path_755{:d => "M-6199.692-2094.888a.749.749,0,0,1-.308-.066.75.75,0,0,1-.442-.684V-2107.3a.75.75,0,0,1,.75-.75.75.75,0,0,1,.75.75v10l2.765-2.443a.75.75,0,0,1,1.059.065.75.75,0,0,1-.065,1.059l-4.011,3.544A.75.75,0,0,1-6199.692-2094.888Z", "data-name" => "Path 755", :fill => "#1f1f1f", :transform => "translate(6207.348 2107.305)"}
                            %rect#Rectangle_23985{"data-name" => "Rectangle 23985", :fill => "none", :height => "24", :width => "24"}
                          %span.txt-1 Sort by
                      .flex-item
                        %button.action-btn.showMobileProductFilter
                          / prettier-ignore
                          %svg#icon_filter.svg--icon{"data-name" => "icon/filter", :height => "24", :viewbox => "0 0 24 24", :width => "24", :xmlns => "http://www.w3.org/2000/svg"}
                            %rect#Rectangle_23407{"data-name" => "Rectangle 23407", :fill => "none", :height => "24", :width => "24"}
                            %path#Path_200150{:d => "M13.224,4.2h-.7a1.8,1.8,0,0,0-3.4,0H3.6a.6.6,0,0,0,0,1.2H9.117a1.8,1.8,0,0,0,3.4,0h.7a.6.6,0,1,0,0-1.2Zm-2.406,1.2a.6.6,0,1,0-.6-.6A.6.6,0,0,0,10.818,5.406ZM3,8.412a.6.6,0,0,1,.6-.6h.7a1.8,1.8,0,0,1,3.4,0h5.515a.6.6,0,1,1,0,1.2H7.708a1.8,1.8,0,0,1-3.4,0H3.6A.6.6,0,0,1,3,8.412Zm3.007.6a.6.6,0,1,0-.6-.6A.6.6,0,0,0,6.007,9.014ZM3.6,11.419a.6.6,0,0,0,0,1.2H9.117a1.8,1.8,0,0,0,3.4,0h.7a.6.6,0,1,0,0-1.2h-.7a1.8,1.8,0,0,0-3.4,0Zm7.818.6a.6.6,0,1,1-.6-.6A.6.6,0,0,1,11.419,12.021Z", "data-name" => "Path 200150", "fill-rule" => "evenodd", :transform => "translate(20.824 4) rotate(90)"}
                          %span.txt-1 Filter
                          %span.txt-2
      #quickViewProductModal.modal.fade.quick-view-produc-modal-wrapper{"aria-hidden" => "true", "aria-labelledby" => "quickViewProductModalLabel", :tabindex => "-1"}
        .modal-dialog.modal-dialog-centered
          .modal-content
            .modal-body
              -if false
                .flex-container
                  .flex-item-1
                    .swiper.quickViewModalSwiperSlider
                      .swiper-wrapper
                        .swiper-slide
                          %img.cmn-img-fluid.slider-img{:alt => "Mirraw", :src => "../../assets/img/catalog/modal_img1.png"}/
                        .swiper-slide
                          %img.cmn-img-fluid.slider-img{:alt => "Mirraw", :src => "../../assets/img/catalog/modal_img1.png"}/
                        .swiper-slide
                          %img.cmn-img-fluid.slider-img{:alt => "Mirraw", :src => "../../assets/img/catalog/modal_img1.png"}/
                        .swiper-slide
                          %img.cmn-img-fluid.slider-img{:alt => "Mirraw", :src => "../../assets/img/catalog/modal_img1.png"}/
                      .swiper-pagination
                  .flex-item-2
                    .p-detail-box
                      %h1.p-title Adidas
                      %span.p-des Blue floral print cotton salwar semi stitched
                      %span.p-code SKU code : 1247927893
                      %span.p-amount-box
                        %span.p-price
                          %i.currency-icon.fa-solid
                          2,446
                        %span.p-mrp
                          %i.currency-icon.fa-solid
                          11,822
                        %span.p-discount (25% OFF)
                      %span.wallet-offer-txt
                        You can earn
                        %span.amount-tx ₹244 Wallet cash
                        on this order
                    .select-size-wrapper
                      %span.ss-txt Select size
                      .chart-scale-box
                        / prettier-ignore
                        %svg#Back_arrow{"data-name" => "Back arrow", :height => "24", :viewbox => "0 0 24 24", :width => "24", :xmlns => "http://www.w3.org/2000/svg"}
                          %rect#Rectangle_24255{"data-name" => "Rectangle 24255", :fill => "none", :height => "24", :width => "24"}
                          %g#ruler{:transform => "translate(2.563 -2.112)"}
                            %path#Path_220146{:d => "M2.359,9A2.359,2.359,0,0,0,0,11.359v5.5a2.359,2.359,0,0,0,2.359,2.359H16.514a2.359,2.359,0,0,0,2.359-2.359v-5.5A2.359,2.359,0,0,0,16.514,9Zm4.718,1.573H5.5V14.5a.786.786,0,0,1-1.573,0V10.573H2.359a.786.786,0,0,0-.786.786v5.5a.786.786,0,0,0,.786.786H16.514a.786.786,0,0,0,.786-.786v-5.5a.786.786,0,0,0-.786-.786H14.941v2.359a.786.786,0,0,1-1.573,0V10.573H11.8V14.5a.786.786,0,0,1-1.573,0V10.573H8.65v2.359a.786.786,0,0,1-1.573,0Z", "data-name" => "Path 220146", "fill-rule" => "evenodd", :transform => "translate(0)"}
                        %span.c-txt CHART
                      .s-size-box-wrap
                        %span.s-size-box.s-selected xs
                        %span.s-size-box s
                        %span.s-size-box m
                        %span.s-size-box l
                        %span.s-size-box xl
                        %span.s-size-box xxl
                        %span.s-size-box xxxl
                    .p-share-cart-box
                      %button.p-add-cart-btn.btn-bg add to cart
                      %button.p-wishlist-btn.p-wishlisted
                        / prettier-ignore
                        %svg#icon_wishlist_inactive{"data-name" => "icon/wishlist/inactive", :height => "22", :viewbox => "0 0 18 18", :width => "22", :xmlns => "http://www.w3.org/2000/svg"}
                          %rect#Rectangle_23416{"data-name" => "Rectangle 23416", :fill => "#fff", :height => "22", :opacity => "0", :width => "22"}
                          %path#Path_197893{:d => "M2.2,7.848a4.835,4.835,0,0,0,1.443,3.634c1.155,1.14,4.979,3.706,5.124,3.848a1.035,1.035,0,0,0,.433.143,1.035,1.035,0,0,0,.433-.143,53.653,53.653,0,0,0,5.124-3.848A4.835,4.835,0,0,0,16.2,7.848,3.8,3.8,0,0,0,9.272,5.639,3.9,3.9,0,0,0,2.2,7.848Z", "data-name" => "Path 197893", :fill => "none", :stroke => "#1f1f1f", "stroke-linecap" => "round", "stroke-linejoin" => "round", "stroke-width" => "1", :transform => "translate(-0.2 -0.737)"}
                      %button.p-share-btn
                        / prettier-ignore
                        %svg#icon_share{"data-name" => "icon/share", :height => "22", :viewbox => "0 0 18 18", :width => "22", :xmlns => "http://www.w3.org/2000/svg"}
                          %g#Rectangle_21663{"data-name" => "Rectangle 21663", :opacity => "0", :stroke => "#f5f5f5", "stroke-width" => "1"}
                            %rect{:height => "22", :stroke => "none", :width => "22"}
                            %rect{:fill => "none", :height => "17", :width => "17", :x => "0.5", :y => "0.5"}
                          %path#Path_200152{:d => "M13.3,12.38a1.928,1.928,0,0,0-1.346.568L7.06,9.888a2.581,2.581,0,0,0,.062-.516,2.581,2.581,0,0,0-.062-.516L11.9,5.826a1.979,1.979,0,0,0,1.4.6,2.137,2.137,0,0,0,2.061-2.212A2.137,2.137,0,0,0,13.3,2a2.137,2.137,0,0,0-2.061,2.212,2.581,2.581,0,0,0,.062.516L6.462,7.757a1.979,1.979,0,0,0-1.4-.6A2.137,2.137,0,0,0,3,9.372a2.137,2.137,0,0,0,2.061,2.212,1.979,1.979,0,0,0,1.4-.6l4.891,3.067a2.224,2.224,0,0,0-.055.479A2.086,2.086,0,0,0,13.3,16.685a2.158,2.158,0,0,0,0-4.305Z", "data-name" => "Path 200152", :fill => "#1f1f1f", :transform => "translate(-0.183 -0.906)"}
                    .view-detail-link-wrapper
                      %a.a-link{:href => "", :target => "_blank"}
                        View all details
                        %i.icon-ar.mg-l.fa-solid.fa-angle-right
    #mobileProductFilter.product-filter-component-container
      .mobile-header-component-container
        .mobile-fixed-header-wrapper
          .flex-container
            .flex-item-1
              %button.action-btn.action-back-btn.hideMobileProductFilter
                / prettier-ignore
                %svg#Back_arrow{"data-name" => "Back arrow", :height => "24", :viewbox => "0 0 24 24", :width => "24", :xmlns => "http://www.w3.org/2000/svg"}
                  %rect#Rectangle_21663{"data-name" => "Rectangle 21663", :fill => "none", :height => "24", :width => "24"}
                  %path#Path_102951{:d => "M1070.769,1253.048a1,1,0,0,0-1.41,0l-5.313,5.332h0a1.993,1.993,0,0,0,0,2.82h0l5.359,5.342a1,1,0,0,0,1.41-1.41l-4.919-4.9a.619.619,0,0,1,0-.877l4.875-4.9A1,1,0,0,0,1070.769,1253.048Z", "data-name" => "Path 102951", :fill => "#1f1f1f", :transform => "translate(-1055.462 -1247.758)"}
            .flex-item-2.header-txt
              %span Filters
      - unless @store_page.blank?
        #filterModal.reveal-modal{data: {reveal:"", v_offset: "0"}, style: 'padding: 0px; width: 100% !important;'}
          - if false #@country_code == 'IN' && params.has_key?(:q)
            = render partial: 'luxe/store/facet_sort_unbxd'
          - else
            = render partial: 'luxe/store/facet_sort'
          %a.close-reveal-modal
          #loader



  / - content_for :page_specific_css do
  /   / = stylesheet_link_tag 'luxe/global', rel: "preload", as: "style",  onload: "this.onload=null;this.rel='stylesheet'"
  /   = stylesheet_link_tag 'luxe/catalog', rel: "preload", as: "style",  onload: "this.onload=null;this.rel='stylesheet'"
  /   = stylesheet_link_tag 'luxe/product-filter', rel: "preload", as: "style",  onload: "this.onload=null;this.rel='stylesheet'"

- else
  %main#mainContentContainer.main-content-container
    %span#mobileMenuOverlay
    .catalog-page-container
      .container-layout-max
        .main-flex-container
          %h1.product-title= product_title
          %h1.text-center No Designs Found
- if @ga_hash_new.present? || @googe_add_hash_new.present?
  :javascript
    window.dataLayer = window.dataLayer || [];
    var gads_items_id = #{@googe_add_hash_new.to_json};
    var view_item_params = #{@ga_hash_new.to_json};
    dataLayer.push({ ecommerce: null });
    dataLayer.push(view_item_params);

:javascript
  var backArrowButton = document.querySelector('.back-arrow-button');
  backArrowButton.addEventListener('click', function(event) {
    event.preventDefault();
    if (window.history.length > 1) {
      window.history.go(-1);
    } else {
      window.location.href = '/';
    }
  });

- content_for :page_specific_js_body do
  = javascript_include_tag 'app'
  / = javascript_include_tag 'store_luxe'
  :javascript
    afterWindowOrTrubolinksLoad(function(){loadScript('#{asset_url("store_luxe.js")}')})
    if (typeof Turbolinks !== 'undefined') {
      var ww = document.documentElement.clientWidth;
      if (ww >= 1000) {
        var headerHeight = $('.desktop-header-component-container:visible').css('height');
        $('#bodyTagContainer').css('padding-top', headerHeight);
      }
    }
    document.addEventListener('turbolinks:load', function() {
      var ww = document.documentElement.clientWidth;
      if (ww >= 1000) {
        var headerHeight = $('.desktop-header-component-container:visible').css('height');
        $('#bodyTagContainer').css('padding-top', headerHeight);
      }
        $(".showSortByOptions").click(function () {
        $("#bodyTagContainer").addClass("noScroll");
        $(".sortByOptionsBox").animate({ height: "290px" }, 300);
        $(".sortByPopUpCoverBlack")
          .delay(200)
          .queue(function (next) {
            $(this).css("opacity", "1");
            $(this).css("height", "100vh");
            next();
          });
        });
        $(".hideSortByOptions, .sortByPopUpCoverBlack").click(function () {
          $("#bodyTagContainer").removeClass("noScroll");
          $(".sortByOptionsBox").delay(300).animate({ height: "0px" }, 300);
          $(".sortByPopUpCoverBlack")
            .delay(100)
            .queue(function (next) {
              $(this).css("opacity", "0");
              next();
            });
          $(".sortByPopUpCoverBlack")
            .delay(200)
            .queue(function (next) {
              $(this).css("height", "0");
              next();
            });
        });
        // ------------------------------------------------------------------
        // Mobile Product Filter
        $(".showMobileProductFilter").click(function () {
          $("#bodyTagContainer").addClass("noScroll");
          $("#mobileProductFilter").css("display", "block");
        });
        $(".hideMobileProductFilter").click(function () {
          $("#mobileProductFilter").css("display", "none");
          $("#bodyTagContainer").removeClass("noScroll");
        });
    });

    $(document).ready( function() {
      var url = window.location.href;
      if (url.indexOf('sort=bstslr') > -1){
        $('.showSortByOptions .txt-2').html('Popularity');
      } else if (url.indexOf('sort=l2h') > -1){
        $('.showSortByOptions .txt-2').html('Price - Low to High');
      } else if (url.indexOf('sort=h2l') > -1){
        $('.showSortByOptions .txt-2').html('Price - High to Low');
      } else if (url.indexOf('sort=discount') > -1){
        $('.showSortByOptions .txt-2').html('Discounts');
      } else if (url.indexOf('sort=new') > -1){
        $('.showSortByOptions .txt-2').html('Newest First');
      } else if (url.indexOf('sort=default') > -1){
        $('.showSortByOptions .txt-2').html('HandPicked');
      } else if (url.indexOf('sort=trending') > -1){
        $('.showSortByOptions .txt-2').html('Trending');
      }
      MR.login.headerIconClickEvent('#catalog-profile-btn','/accounts/sign_in','#loginModal');
    });
    
  :javascript
    if (typeof Turbolinks !== 'undefined') {
      MR.dealTimer.init()
      $('.common-product-card-wrapper').on('click', function(e) {
        e.preventDefault();
        window.location.href = $(this).attr('href');
      });
    }
  
    $(document).on('turbolinks:load', function() {
      MR.dealTimer.init()
      $('.common-product-card-wrapper').on('click', function(e) {
        e.preventDefault();
        window.location.href = $(this).attr('href');
      });
    });



:javascript
  $(document).ready(function() {
    MR.SeoContent.categoryContent()
  })
