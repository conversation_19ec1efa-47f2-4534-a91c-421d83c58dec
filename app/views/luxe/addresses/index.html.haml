- content_for :page_specific_css do
  = stylesheet_link_tag 'luxe/profile-sidebar.css'
  = stylesheet_link_tag 'luxe/address.css'
- content_for :page_specific_js do
  = javascript_include_tag 'luxe/addresses'

= render 'luxe/shared/mobile_header'
/ - content_for :page_specific_js do
/   = javascript_include_tag 'addresses'
/ / = render partial: 'layouts/payment_steps'
/ .row
/   .large-6.medium-8.small-12.columns.small-centered
/     .address-label
/       Addresses
/     - @addresses.each do |address|
/       .billing_address.bordered_block
/         = link_to '', user_address_path(address), method: 'DELETE', class: 'right close-icon', data: { confirm: 'Are you sure you want to delete this address?'}
/         %table{style: 'width: 90%;'}
/           %tr
/             %td
/               Name :
/             %td
/               = address.name
/           %tr
/             %td
/               Street Address :
/             %td
/               = address.street_address
/           %tr
/             %td
/               Landmark :
/             %td
/               = address.landmark
/           %tr
/             %td
/               City :
/             %td
/               = address.city
/           %tr
/             %td
/               State :
/             %td
/               = address.state
/           %tr
/             %td
/               Country :
/             %td
/               = address.country
/           %tr
/             %td
/               Pincode :
/             %td
/               = address.pincode
/           %tr
/             %td{colspan: 2}
/               - if address.default == 0
/                 = form_tag [@user, address], method: 'PUT' do
/                   = radio_button_tag 'address[default]', '1', false, class: 'mark_as_default', id: "default_address_#{address.id}"
/                   = label_tag :mark_as_default, 'Mark as default billing address', for: "default_address_#{address.id}"
/                   = hidden_field_tag :address_id, address.id
/                   = submit_tag :save, class: 'hide'
/         %ul.small-block-grid-2
/           %li{style: 'padding-bottom: 0em;'}= link_to 'SHIP HERE', shipping_address_orders_path(shipping_id: address.id), class: 'button tiny expand secondary'
/           %li{style: 'padding-bottom: 0em;'}= link_to 'EDIT ADDRESS', edit_user_address_path(address), class: 'button tiny expand secondary'
/   .large-6.medium-6.small-8.columns.small-centered
/     = link_to 'Add New Address', new_user_address_path , class: 'success button tiny text-center expand', style: 'margin: 3.7em 0;'
/   .large-6.medium-6.small-8.columns.small-centered
/     = link_to 'Back', new_order_path , class: 'button tiny info text-center expand', style: 'margin: 3.7em 0;'
%main#mainContentContainer.main-content-container
  %span#mobileMenuOverlay
  .address-page-container
    .container-layout-max
      .container-fluid.d-no-padding
        .row.d-no-margin.align-items-start
          .col-12.d-no-padding.d-width-70
            .address-details-wrapper
              %h1.m-view.a-headig Address
              .address-list-box
                / Use this class 'activeAddress' to show active
                -@addresses.each do |address|
                  .address-item-wrap.common-addres-info-box
                    .flex-container
                      .flex-item-1
                        %h1.a-heading
                          = address.name
                          %span.a-label Home
                        %span.a-detail= address.street_address
                        - if address.default == 0
                          = form_tag [@user, address], method: 'PUT' do
                            = radio_button_tag 'address[default]', '1', false, class: 'mark_as_default', id: "default_address_#{address.id}"
                            = label_tag :mark_as_default, 'Set as default billing address', for: "default_address_#{address.id}"
                            = hidden_field_tag :address_id, address.id
                            = submit_tag :save, class: 'hide btn btn-dark'
                      .flex-item-2.d-view
                        = link_to shipping_address_orders_path(shipping_id: address.id) do
                          %button.action-btn.ship-btn{:type => "button"} Ship Here
                        = link_to edit_user_address_path(address), class: 'button tiny expand secondary' do
                          %button.action-btn.edit-btn{:type => "button"} Edit Address
                        %button.action-btn.delete-btn{"data-bs-target" => "#deleteAddressModal_#{address.id}", "data-bs-toggle" => "modal", :type => "button"}
                          Delete Address
                      .flex-item-3.m-view
                        = link_to 'SHIP HERE', shipping_address_orders_path(shipping_id: address.id), class: 'button tiny expand secondary'

                        .dropdown.action-dropdown-btn-box
                          %button#dropdownAddressActionBtn.btn.btn-secondary.dropdown-toggle.act-btn{"aria-expanded" => "false", "data-bs-toggle" => "dropdown", :type => "button"}
                            %i.fa-solid.fa-ellipsis-vertical
                          %ul.dropdown-menu{"aria-labelledby" => "dropdownAddressActionBtn"}
                            %li
                              = link_to edit_user_address_path(address), class: 'button tiny expand secondary' do
                                %button.action-btn.edit-btn{:type => "button"} Edit Address
                            %li
                              %button.action-btn.delete-btn{"data-bs-target" => "#deleteAddressModal_#{address.id}", "data-bs-toggle" => "modal", :type => "button"}
                                Delete Address
                      / ************************* DELETE MODAL *************************
                      .modal.fade.drawer.delete-address-modal-wrapper{id: "deleteAddressModal_#{address.id}","aria-hidden" => "true", "aria-labelledby" => "deleteAddressModalLabel", :tabindex => "-1"}
                        .modal-dialog.modal-dialog-centered
                          .modal-content
                            .modal-body
                              %h1.heading Are you sure you want to delete this address?
                              .footer-btn-wrapper
                                .flex-container
                                  .flex-item
                                    %button.action-btn.common-white-btn.cancel-btn{"aria-label" => "Close", "data-bs-dismiss" => "modal"}
                                      Cancel
                                  .flex-item
                                    = link_to user_address_path(address), method: 'DELETE', class: 'right close-icon' do
                                      %button.action-btn.common-black-btn.delete-btn{"aria-label" => "Close", "data-bs-dismiss" => "modal"}
                                        delete
                .address-item-wrap.common-addres-info-box.new-address-wrap
                  %a.na-flex-container{:href => new_user_address_path}
                    %div
                      %div
                        / prettier-ignore
                        %svg#Group_148136{"data-name" => "Group 148136", :height => "36", :viewbox => "0 0 36 36", :width => "36", :xmlns => "http://www.w3.org/2000/svg"}
                          %rect#Rectangle_24057{"data-name" => "Rectangle 24057", :fill => "#fff", :height => "36", :opacity => "0", :width => "36"}
                          %path#Union_13{:d => "M13.244,34,3.883,24.273A13.885,13.885,0,0,1,0,14.574,12.853,12.853,0,0,1,3.883,5.414a13.271,13.271,0,0,1,9.317-3.8h.093a13.352,13.352,0,0,1,6.771,1.847,7.283,7.283,0,0,0,6.321,9.462,12.693,12.693,0,0,1,.108,1.647,13.869,13.869,0,0,1-3.88,9.7L13.249,34v0ZM10.12,11.743a4.421,4.421,0,1,0,3.129-1.289h-.018A4.434,4.434,0,0,0,10.12,11.743ZM21.341,5.751a5.749,5.749,0,1,1,5.75,5.747A5.76,5.76,0,0,1,21.341,5.751Zm3.1,0a.442.442,0,0,0,.446.442H26.65V7.96a.442.442,0,1,0,.884,0V6.193H29.3a.442.442,0,0,0,0-.884H27.534V3.538a.442.442,0,0,0-.884,0V5.309H24.883A.442.442,0,0,0,24.437,5.751Z", "data-name" => "Union 13", :fill => "#f19474", :transform => "translate(1.5 1)"}
                      %span.a-new add new address
              .m-view.continue-btn-wrapper.m-bottom-fixed.mobilePositionFixedToggle
                %button.common-black-bg-btn.continue-btn{:type => "submit"} continue
    / ************************* ADDRESS ADDED TOAST *************************
    .address-added-successfully-toast-wrapper.p-3
      #addressAddedToastContent.toast.iac-toast-body{"aria-atomic" => "true", "aria-live" => "assertive"}
        .toast-body Address added successfully
-# %script{:async => "", :src => "https://www.googletagmanager.com/gtag/js?id=AW-10987265357"}
-# :javascript
  -# window.dataLayer = window.dataLayer || [];
  -# function gtag(){dataLayer.push(arguments);}
  -# gtag('js', new Date());
  -# gtag('config', 'AW-10987265357');
:javascript
  var ww = document.documentElement.clientWidth;
  // ------------------------------------------------------------------
  // Set Body Top Padding For Fixed Header
  if (ww >= 1000) {
    var headerHeight = $('.desktop-header-component-container:visible').css('height');
    $('#bodyTagContainer').css('padding-top', headerHeight);
  }
-if @cart.present?
  - market_rate = CurrencyConvert.countries_marketrate[@country_code] || 1
  - pro_totals, max_total, shipping_currency = get_cart_total_information(@country_code, @rate)
  - shipping = (shipping_currency.to_f * market_rate).round(2)
  :javascript
    window.dataLayer = window.dataLayer || [];
    $(document).on('click', '#address_collect_submit', function(e)  {
      if (isFormFilled()) {
        var shipping_params = #{@ga_hash_new.to_json};
        shipping_params.ecommerce.shipping = #{shipping}
        dataLayer.push({ ecommerce: null });
        dataLayer.push(shipping_params);
      }
    });
    function isFormFilled() {
      var form = $('#new_address');
      var requiredFields = form.find('input[required], textarea[required]');
      for (var i = 0; i < requiredFields.length; i++) {
        if (!requiredFields[i].value.trim()) {
          return false;
        }
      }
      return true;
    }