- content_for :page_specific_css do
  = stylesheet_link_tag 'luxe/add-new-or-edit-address'
- content_for :page_specific_js do
  = javascript_include_tag 'luxe/addresses'
- via_login = 'accounts/sessions, accounts/registrations'.include?(params[:controller]) && 'guest_login, accounts/create_guest'.include?(params[:action])
- action_url = via_login ? accounts_create_guest_path : store_addresses_path

%main#mainContentContainer.main-content-container
  %span#mobileMenuOverlay
  .add-new-or-edit-address-page-container
    .container-layout-max
      .horizontal-padding
        .container-fluid
          .row
            .col-12
              .common-breadcrumb-wrapper
                %ul.breadcrumb-list
                  %li.b-item
                    %span.b-link{:href => "/user/profile"} Profile
                  %li.b-item
                    %span.b-link{:href => "/user/addresses"} Add new Address
        .container-fluid.brd-bottom
          .row.justify-content-between
            / .col-12.m-view
            /   .detect-location-wrapper.m-view
            /     %h1.e-heading Add new address
            /     %button.common-white-btn.detect-btn{"data-bs-target" => "#grantLocationAccessModal", "data-bs-toggle" => "modal"}
            /       / prettier-ignore
            /       %svg{:height => "20", :viewbox => "0 0 20 20", :width => "20", :xmlns => "http://www.w3.org/2000/svg"}
            /         %path#my-location{:d => "M10,6.758a3.516,3.516,0,0,1,2.58,1.064,3.59,3.59,0,0,1,0,5.106A3.519,3.519,0,0,1,10,13.992a3.519,3.519,0,0,1-2.58-1.064,3.59,3.59,0,0,1,0-5.106A3.516,3.516,0,0,1,10,6.758Zm8.124,2.723H20v1.787H18.124A7.846,7.846,0,0,1,15.8,16.141,8.074,8.074,0,0,1,10.917,18.5v1.872H9.083V18.5a8.138,8.138,0,0,1-4.861-2.361,7.8,7.8,0,0,1-2.345-4.873H0V9.481H1.876A7.8,7.8,0,0,1,4.222,4.609,8.134,8.134,0,0,1,9.083,2.247V.375h1.834V2.247A8.08,8.08,0,0,1,15.8,4.609,7.84,7.84,0,0,1,18.124,9.481Zm-8.1,7.234a6.132,6.132,0,0,0,4.5-1.851,6.109,6.109,0,0,0,1.855-4.49,6.1,6.1,0,0,0-1.855-4.489,6.139,6.139,0,0,0-4.5-1.851A6.2,6.2,0,0,0,5.5,5.885a6.074,6.074,0,0,0-1.876,4.49A6.072,6.072,0,0,0,5.5,14.864,6.2,6.2,0,0,0,10.021,16.715Z", :fill => "rgba(0,0,0,0.8)", :transform => "translate(0 -0.375)"}
            /       %span detect my location

        = form_for @address || @user.try{|u| u.addresses.new} || Address.new, url: action_url, validate: true, html: {method: 'POST'} do |f|
          .address-details-content-wrapper
            .container-fluid
              .row.justify-content-between
                .col-12.m-view
                  %h1.e-heading Enter Address details
                .col-12.col-sm-6
                  .row.justify-content-between
                    = hidden_field_tag 'country_code', @actual_country, {:value => @country_code}
                    - country_name =  @address.try(:country).presence || @actual_country
                    .col-12
                      .input-flex-container.common-input-box
                        .i-flex-item-1
                          %span.i-label Country
                        .i-flex-item-2
                          = f.select :country, @countries,  {:selected => country_name, label: false}, {class: 'select-country-name'}
                    .col-12
                      .input-flex-container.common-input-box
                        .i-flex-item-1
                          %span.i-label Name
                        .i-flex-item-2
                          = f.text_field :name, {label: false, class: 'i-input-control', placeholder: 'Full Name', type: 'text', required: true}
                          %span.i-label#name_error
                    .col-12
                      .input-flex-container.common-input-box
                        .i-flex-item-1
                          %span.i-label Email
                        .i-flex-item-2
                          = f.text_field :email, {label: false, class: 'i-input-control', placeholder: 'Email', type: 'email', required: true}
                          %span.i-label#email_error
                    .col-12
                      .input-flex-container.common-input-box
                        .i-flex-item-1
                          %span.i-label Mobile
                        .i-flex-item-2
                          -if @actual_country == 'India'
                            = text_field_tag 'address[dial_code]', '', style: 'text-align: center;', id: 'dial_code_text', autocomplete: 'none', label: 'dial_code', disabled: true, class: 'i-input-control'
                            = f.text_field :phone, {required: true, label: false, minlength: '8', maxlength: '10',  type: 'tel', class: 'i-input-control mobileNumberTelInput', placeholder: 'Mobile Number', style: "border-left: 2px solid #e9ebf0;padding-left: 10px;font-family: 'Gilroy';"}
                            %span.i-label#phone_error
                          -else
                            = text_field_tag 'address[dial_code]', '', style: 'text-align: center;', id: 'dial_code_text', autocomplete: 'none', label: 'dial_code', disabled: true, class: 'i-input-control'
                            = f.text_field :phone, {required: true, label: false, minlength: '8', maxlength: '10',  type: 'tel', class: 'i-input-control mobileNumberTelInput', placeholder: 'Mobile Number', style: "border-left: 2px solid #e9ebf0;padding-left: 10px;font-family: 'Gilroy';"}
                            %span.i-label#phone_error
                    .col-12
                      .input-flex-container.common-input-box
                        .i-flex-item-1
                          %span.i-label Address 1
                        .i-flex-item-2
                          = f.text_field :street_address, {required: true, label: false, class: 'i-input-control', placeholder: 'Flat No, Floor, Building Name', type: 'text', minlength: '3'}
                          %span.i-label#address_error
                .col-12.col-sm-6
                  .row.justify-content-between
                    .col-12
                      .input-flex-container.common-input-box
                        .i-flex-item-1
                          %span.i-label Address 2
                        .i-flex-item-2
                          = f.text_field :street_address_line_2, {label: false, class: 'i-input-control', placeholder: 'Colony, Street, Locality', type: 'text'}
                    .col-12
                      .input-flex-container.common-input-box
                        .i-flex-item-1
                          %span.i-label PIN CODE
                        .i-flex-item-2
                          = f.text_field :pincode, {required: true, type: 'text', maxlength: '15', class: 'i-input-control', label: false}
                          %span.i-label#pincode_error
                    .col-12
                      .input-flex-container.common-input-box
                        .i-flex-item-1
                          %span.i-label City/Town
                        .i-flex-item-2
                          = f.text_field :city, {placeholder: 'City', required: true, minlength: 3, class: 'i-input-control', type: 'text', label: false}
                          %span.i-label#city_error
                    .col-12
                      .input-flex-container.common-input-box
                        .i-flex-item-1
                          %span.i-label State
                        .i-flex-item-2
                          = f.text_field :state, {placeholder: 'State', required: true, minlength: 2, class: 'i-input-control', type: 'text', label: false}
                          %span.i-label#state_error
              .row.justify-content-between
                .col-12.col-sm-8
                  .input-flex-container.common-input-box
                    .i-flex-item-1
                -#       %span.i-label Type
                -#     .i-flex-item-2.d-view
                -#       .type-radio-list
                -#         .radio-item
                -#           .common-radio-btn-grp
                -#             %input#type-radio-1{:checked => "checked", :name => "type-radio", :type => "radio", :value => "home"}/
                -#             %label.radio-label{:for => "type-radio-1"} HOME
                -#         .radio-item
                -#           .common-radio-btn-grp
                -#             %input#type-radio-2{:name => "type-radio", :type => "radio", :value => "work"}/
                -#             %label.radio-label{:for => "type-radio-2"} WORK
                -#         .radio-item
                -#           .common-radio-btn-grp
                -#             %input#type-radio-3{:name => "type-radio", :type => "radio", :value => "others"}/
                -#             %label.radio-label{:for => "type-radio-3"} OTHERS
                -#     .i-flex-item-2.m-view
                -#       %select.form-select.m-type-select{"aria-label" => "Default select example"}
                -#         %option{:selected => "selected", :value => "home"} HOME
                -#         %option{:value => "work"} WORK
                -#         %option{:value => "others"} OTHERS
                -# .col-12.col-sm-8
                -#   .checkbox-item.checkbox-wrap
                -#     %label.common-checkbox-grp
                -#       %span.l-label Save for future orders
                -#       %input{:checked => "checked", :type => "checkbox", :value => "Save for future orders"}/
                      .control-indicator
          .container-fluid.m-bottom-fixed.mobilePositionFixedToggle
            .row.justify-content-between
              .col-12.col-sm-6
                / Added 'disabled' Attribute to apply disabled btn styling
                = f.submit 'CONTINUE TO PAYMENT', id: :address_collect_submit, class: 'common-black-bg-btn save-btn'
  / ************************* GRANT LOCATION ACCESS MODAL *************************
  #grantLocationAccessModal.modal.fade.drawer.grant-location-access-modal-wrapper{"aria-hidden" => "true", "aria-labelledby" => "grantLocationAccessModalLabel", :tabindex => "-1"}
    .modal-dialog.modal-dialog-centered
      .modal-content
        .modal-body
          .location-img-box
            %img.cmn-img-fluid.location-img{:alt => "Mirraw", :src => "./../assets/img/location.png"}/
          %h1.heading Grant location access
          %span.sub-heading We need your location to provide the accurate results and give you the best experience.
          .next-btn-wrapper.m-bottom-fixed
            %button.common-black-bg-btn.location-access-btn{"aria-label" => "Close", "data-bs-dismiss" => "modal", :type => "button"}
              grant location access


= javascript_include_tag 'luxe/address', :defer => true, 'data-turbolinks-track' => 'reload'

:javascript
  var ww = document.documentElement.clientWidth;
  // ------------------------------------------------------------------
  // Set Body Top Padding For Fixed Header
  if (ww >= 1000) {
    var headerHeight = $('.desktop-header-component-container:visible').css('height');
    $('#bodyTagContainer').css('padding-top', headerHeight);
  }
  
:javascript
  $(window).scroll(function() {
    var scroll = $(window).scrollTop();
    if (scroll >= 226) {
      $(".mobilePositionFixedToggle").removeClass("m-bottom-fixed");
    }
    else if (scroll <= 226){
      $(".mobilePositionFixedToggle").addClass("m-bottom-fixed");
    }
  });

