/ = render partial: 'layouts/payment_steps'
= render 'luxe/shared/mobile_header'
= render partial: 'luxe/addresses/form'

-if @cart.present?
  - market_rate = CurrencyConvert.countries_marketrate[@country_code] || 1
  - pro_totals, max_total, shipping_currency = get_cart_total_information(@country_code, @rate)
  - shipping = (shipping_currency.to_f * market_rate).round(2)

  :javascript
    window.dataLayer = window.dataLayer || [];
    $(document).on('click', '#address_collect_submit', function(e)  {
      if (isFormFilled()) {
        var shipping_params = #{@ga_hash_new.to_json};
        shipping_params.ecommerce.shipping = #{shipping}
        dataLayer.push({ ecommerce: null });
        dataLayer.push(shipping_params);
      }
    });
    function isFormFilled() {
      var form = $('#new_address');
      var requiredFields = form.find('input[required], textarea[required]');
      for (var i = 0; i < requiredFields.length; i++) {
        if (!requiredFields[i].value.trim()) {
          return false;
        }
      }
      return true;
    }