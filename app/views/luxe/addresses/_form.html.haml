- content_for :page_specific_css do
  = stylesheet_link_tag 'luxe/add-new-or-edit-address'
- content_for :page_specific_js do
  = javascript_include_tag 'luxe/addresses'
/ .row
/   #address_heading
/     .text-center= @address_type.try(:titleize)
/   .billing_address.large-6.medium-8.small-12.columns.small-centered
/     = form_for [@user, @address], validate: true do |f|
/       - if is_domestic?
/         .row
/           .columns
/             = f.text_field :name, placeholder: "Receiver's Name", type: 'text', required: true
/       -else
/         .row
/           .columns
/             = f.text_field :first_name, placeholder: 'First Name', type: 'text', required: true
/         .row
/           .columns
/             = f.text_field :last_name, placeholder: 'Last Name', type: 'text', required: true
/       - unless @address.try(:country).present?
/         .row
/           = hidden_field_tag 'country_code', @actual_country
/       .row
/         .columns
/           = f.select :country, @countries, selected: @address.try(:country)
/       - if is_domestic?
/         .row
/           .columns
/             = f.text_field :street_address, placeholder: 'House No, Colony, Street', required: true, type: 'text', label: 'Flat No / Floor / Building Name', minlength: 3
/         .row
/           .columns
/             = f.text_field :street_address_line_2, placeholder: 'Locality', required: true, type: 'text', label: 'Colony / Street / Locality', minlength: 3
/         .row
/           .columns
/             = f.text_field :street_address_line_3, placeholder: 'Landmark (optional)', type: 'text', label: 'Landmark', minlength: 3
/       - elsif is_saudi_arabia?
/         .row
/           .columns
/             = f.text_field :street_address_line_1, placeholder: 'Apartment / Flat Number', required: true, type: 'text', label: 'Apartment / Flat Number', minlength: 3
/         .row
/           .columns
/             = f.text_field :street_address_line_2, placeholder: 'Building Name / Number', required: true, type: 'text', label: 'Building Name / Number', minlength: 3
/         .row
/           .columns
/             = f.text_field :street_address_line_3, placeholder: 'Street Name / Number', required: true, type: 'text', label: 'Street Name / Number', minlength: 3
/       - else
/         .row
/           .columns
/             = f.text_field :street_address, placeholder: 'Street Address', required: true, type: 'text', rows: 3, label: 'Address', minlength: 3
/       .row
/         #showpincodefields
/           .columns.small-6.columns.medium-6.large-6
/             - pin_or_zip = is_domestic? ? 'Pincode' : 'Zip Code / Postal Code'
/             - if @actual_country == 'India'
/               = f.text_field :pincode, placeholder: pin_or_zip, required: true, type: 'tel', maxlength: '6',pattern: '^[1-9]\d{5}$',title:'Please enter 6 digit pincode'
/             - else
/               = f.text_field :pincode, placeholder: pin_or_zip, required: true, type: 'text', maxlength: '15'
/             #pincode_format_notice.pincode_format
/         #state-container.columns.small-6.medium-6.large-6
/           = f.text_field :state, placeholder: 'State', required: true, value: @address.try(:state)
/       .row
/         .columns
/           = f.text_field :city, placeholder: 'City', required: true, minlength: 3
/       .row.collapse
/         - unless @address.try(:phone).present?
/           .small-2.medium-2.large-2.dial_code_block.columns{style: 'display: none'}
/             = text_field_tag 'address[dial_code]', '', style: 'text-align: center;', id: 'dial_code_text', autocomplete: 'none'
/         #address_phone_conatainer.columns
/           = f.text_field :phone, placeholder: 'Mobile Number ', required: true
/       - if params[:address_type].blank? && @address.new_record? && @user.default_address.blank?
/         .row

/             %label.shipping_confirm_checkbox
/               = check_box_tag :shipping_address, 1, checked: true
/               = hidden_field_tag :ship_to_same_address, 1
/               %span= 'Ship to same address'
/       = hidden_field_tag :address_id, @address.id
/       .row
/         .columns
/           = f.submit 'SAVE AND CONTINUE', id: :address_collect_submit, class: 'button tiny expand success order_flow_submit_button sticky-button'
%main#mainContentContainer.main-content-container
  %span#mobileMenuOverlay
  .add-new-or-edit-address-page-container
    .container-layout-max
      .horizontal-padding
        .container-fluid
          .row
            .col-12
              .common-breadcrumb-wrapper
                %ul.breadcrumb-list
                  %li.b-item
                    %span.b-link{:href => "/user/profile"} Profile
                  %li.b-item
                    %span.b-link{:href => "/user/addresses"} Add new Address
        .container-fluid.brd-bottom
          .row.justify-content-between
            .col-12.m-view
              .detect-location-wrapper.m-view
                / %h1.e-heading Add new address
                / %button.common-white-btn.detect-btn{"data-bs-target" => "#grantLocationAccessModal", "data-bs-toggle" => "modal"}
                /   / prettier-ignore
                /   %svg{:height => "20", :viewbox => "0 0 20 20", :width => "20", :xmlns => "http://www.w3.org/2000/svg"}
                /     %path#my-location{:d => "M10,6.758a3.516,3.516,0,0,1,2.58,1.064,3.59,3.59,0,0,1,0,5.106A3.519,3.519,0,0,1,10,13.992a3.519,3.519,0,0,1-2.58-1.064,3.59,3.59,0,0,1,0-5.106A3.516,3.516,0,0,1,10,6.758Zm8.124,2.723H20v1.787H18.124A7.846,7.846,0,0,1,15.8,16.141,8.074,8.074,0,0,1,10.917,18.5v1.872H9.083V18.5a8.138,8.138,0,0,1-4.861-2.361,7.8,7.8,0,0,1-2.345-4.873H0V9.481H1.876A7.8,7.8,0,0,1,4.222,4.609,8.134,8.134,0,0,1,9.083,2.247V.375h1.834V2.247A8.08,8.08,0,0,1,15.8,4.609,7.84,7.84,0,0,1,18.124,9.481Zm-8.1,7.234a6.132,6.132,0,0,0,4.5-1.851,6.109,6.109,0,0,0,1.855-4.49,6.1,6.1,0,0,0-1.855-4.489,6.139,6.139,0,0,0-4.5-1.851A6.2,6.2,0,0,0,5.5,5.885a6.074,6.074,0,0,0-1.876,4.49A6.072,6.072,0,0,0,5.5,14.864,6.2,6.2,0,0,0,10.021,16.715Z", :fill => "rgba(0,0,0,0.8)", :transform => "translate(0 -0.375)"}
                /   %span detect my location
        = form_for [@user, @address], validate: true do |f|
          .address-details-content-wrapper
            .container-fluid
              .row.justify-content-between
                .col-12.m-view
                  %h1.e-heading Enter Address Details
                .col-12.col-sm-6
                  .row.justify-content-between
                    = hidden_field_tag :address_id, @address.id
                    = hidden_field_tag 'country_code', @actual_country
                    - country_name =  @address.try(:country).presence || @actual_country
                    .col-12
                      .input-flex-container.common-input-box
                        .i-flex-item-1
                          %span.i-label Country
                        .i-flex-item-2
                          = f.select :country, @countries, {:selected => country_name, label: false}, {class: 'select-country-name'}
                    .col-12
                      .input-flex-container.common-input-box
                        .i-flex-item-1
                          %span.i-label Name
                        .i-flex-item-2
                          = f.text_field :name, {label: false, class: 'i-input-control', placeholder: 'Full Name', type: 'text', required: true}
                          %span.i-label#name_error
                    .col-12
                      .input-flex-container.common-input-box
                        .i-flex-item-1
                          %span.i-label Email
                        .i-flex-item-2
                          = f.text_field :email, {label: false, class: 'i-input-control', placeholder: 'Email', type: 'email', required: true, value: @email}
                          %span.i-label#email_error
                    .col-12
                      .input-flex-container.common-input-box
                        .i-flex-item-1
                          %span.i-label Mobile
                        .i-flex-item-2
                          -if @actual_country == 'India'
                            = text_field_tag 'address[dial_code]', '', style: 'text-align: center;', id: 'dial_code_text', autocomplete: 'none', label: 'dial_code', disabled: true, class: 'i-input-control'
                            = f.text_field :phone, {required: true,value: @phone, label: false, minlength: '8', maxlength: '10',  type: 'tel', class: 'i-input-control mobileNumberTelInput', placeholder: 'Mobile Number', style: "border-left: 2px solid #e9ebf0;padding-left: 10px;font-family: 'Gilroy';"}
                            %span.i-label#phone_error
                          -else
                            = text_field_tag 'address[dial_code]', '', style: 'text-align: center;', id: 'dial_code_text', autocomplete: 'none', label: 'dial_code', disabled: true, class: 'i-input-control'
                            = f.text_field :phone, {required: true,value: @phone, label: false, minlength: '8', maxlength: '10',  type: 'tel', class: 'i-input-control mobileNumberTelInput', placeholder: 'Mobile Number', style: "border-left: 2px solid #e9ebf0;padding-left: 10px;font-family: 'Gilroy';"}
                            %span.i-label#phone_error
                    .col-12
                      .input-flex-container.common-input-box
                        .i-flex-item-1
                          %span.i-label Address 1
                        .i-flex-item-2
                          = f.text_field :street_address, {required: true, label: false, class: 'i-input-control', placeholder: 'Flat No, Floor, Building Name', type: 'text', minlength: '3'}
                          %span.i-label#address_error
                .col-12.col-sm-6
                  .row.justify-content-between
                    .col-12
                      .input-flex-container.common-input-box
                        .i-flex-item-1
                          %span.i-label Address 2
                        .i-flex-item-2
                          = f.text_field :street_address_line_2, {label: false, class: 'i-input-control', placeholder: 'Colony, Street, Locality', type: 'text'}
                    .col-12
                      .input-flex-container.common-input-box
                        .i-flex-item-1
                          %span.i-label PIN CODE
                        .i-flex-item-2
                          = f.text_field :pincode, {required: true, type: 'text', maxlength: '15', class: 'i-input-control', label: false}
                          %span.i-label#pincode_error
                    .col-12
                      .input-flex-container.common-input-box
                        .i-flex-item-1
                          %span.i-label City/Town
                        .i-flex-item-2
                          = f.text_field :city, {placeholder: 'City', required: true, minlength: 3, class: 'i-input-control', type: 'text', label: false}
                          %span.i-label#city_error
                    .col-12
                      .input-flex-container.common-input-box
                        .i-flex-item-1
                          %span.i-label State
                        .i-flex-item-2
                          = f.text_field :state, {placeholder: 'State', required: true, minlength: 2, class: 'i-input-control', type: 'text', label: false}
                          %span.i-label#state_error
              .row.justify-content-between
                .col-12.col-sm-8
                  .input-flex-container.common-input-box
                    .i-flex-item-1
          .container-fluid.m-bottom-fixed.mobilePositionFixedToggle
            .row.justify-content-between
              .col-12.col-sm-6
                / Added 'disabled' Attribute to apply disabled btn styling
                = f.submit 'CONTINUE TO PAYMENT', id: :address_collect_submit, class: 'common-black-bg-btn save-btn'
  / ************************* GRANT LOCATION ACCESS MODAL *************************
  #grantLocationAccessModal.modal.fade.drawer.grant-location-access-modal-wrapper{"aria-hidden" => "true", "aria-labelledby" => "grantLocationAccessModalLabel", :tabindex => "-1"}
    .modal-dialog.modal-dialog-centered
      .modal-content
        .modal-body
          .location-img-box
            %img.cmn-img-fluid.location-img{:alt => "Mirraw", :src => "./../assets/img/location.png"}/
          %h1.heading Grant location access
          %span.sub-heading We need your location to provide the accurate results and give you the best experience.
          .next-btn-wrapper.m-bottom-fixed
            %button.common-black-bg-btn.location-access-btn{"aria-label" => "Close", "data-bs-dismiss" => "modal", :type => "button"}
              grant location acces

= javascript_include_tag 'luxe/address', :defer => true, 'data-turbolinks-track' => 'reload'



:javascript
  var ww = document.documentElement.clientWidth;
  // ------------------------------------------------------------------
  // Set Body Top Padding For Fixed Header
  if (ww >= 1000) {
    var headerHeight = $('.desktop-header-component-container:visible').css('height');
    $('#bodyTagContainer').css('padding-top', headerHeight);
  }

:javascript
  $(window).scroll(function() {
    var scroll = $(window).scrollTop();
    if (scroll >= 350) {
      $(".mobilePositionFixedToggle").removeClass("m-bottom-fixed");
    }
    else if (scroll <= 350){
      $(".mobilePositionFixedToggle").addClass("m-bottom-fixed");
    }
  });