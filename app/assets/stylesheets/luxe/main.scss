@import 'fonts.scss';
@import 'mixin_variables.scss';
@import 'variables_luxe';

html {
  height: 100%;
}

body {
  color: $black;
  margin: 0 auto;
  min-height: 100%;
  letter-spacing: 0px;
  background: $white;
  @include medium();

  &#bodyTagContainer {
    &.noScroll {
      overflow: hidden;
    }

    &.setRelative {
      position: relative;
    }
  }
}

#mainContentContainer {
  #mobileMenuOverlay {
    content: '';
    left: 0;
    right: 0;
    opacity: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.4);
    -webkit-transition-delay: 0.1s;
    -o-transition-delay: 1s;
    transition-delay: 1s;
    -webkit-transition: opacity 0.5s;
    -o-transition: opacity 0.5s;
    transition: opacity 0.5s;

    &.toggleOverlay {
      content: '';
      top: 0;
      opacity: 1;
      z-index: $zIdxSideNavOverlay;
      position: absolute;
    }

    &.toggleOverlayForCartSidenavDrawer {
      content: '';
      top: 0;
      opacity: 1;
      z-index: $zIdxCartRightSidenavOverlay;
      position: absolute;
    }
  }
}

p,
span,
h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  color: $black;
}

.m-pointer {
  cursor: pointer;
}

ol,
ul {
  margin: 0;
  padding: 0;
}

a {
  color: $black1;
  text-decoration: none;

  &:hover {
    color: $black1;
    text-decoration: none;
  }
}

/* MAX WIDTH */
.container-layout-max {
  @include layoutMax();
}

.main-content-container {
  min-height: calc(100vh - 747px);

  @media screen and (max-width:767px) {
    overflow: hidden !important;
  }
}

.horizontal-padding {
  padding: 0 105px;

  @media only screen and (min-width: 1000px) and (max-width: 1280px) {
    padding: 0 60px;
  }
}

.vertical-padding {
  padding: 105px 0;
}

.horizontal-margin {
  margin: 0 105px;
}

.vertical-margin {
  margin: 55px 0;
}

.all-side-padding {
  padding: 105px 105px;
}

.all-side-margin {
  margin: 55px 55px;
}

.common-heading {
  @include commonHeading();
  font-family: 'Cormorant Garamond';
  text-align: center !important;
}

.cmn-img-fluid {
  @include imgFluid();
}

.d-view {
  display: block;
}

.m-view {
  display: none;
}

.btn-bg {
  color: $white;
  border-radius: 8px;
  background-size: cover;
  background-repeat: no-repeat;
  background-color: $pGreen;
}

.dropdown-menu {
  background: #f8faff;
  border: 1px solid #eeee;

  .dropdown-item:focus,
  .dropdown-item:hover {
    background-color: $white;
  }
}

// LightGallery
.lg-container {
  .lg-sub-html {
    display: none;
  }

  .lg-thumb-item img {
    @include fitCover();
  }
}

// Swiper
.swiper {
  width: 100%;
  height: 100%;

  &:hover {

    .swiper-prev-btn,
    .swiper-next-btn {
      opacity: 1;

      &.swiper-button-disabled {
        opacity: 0.35;
      }
    }
  }

  .swiper-prev-btn,
  .swiper-next-btn {
    opacity: 0;
    width: 40px;
    height: 40px;
    color: $white;
    border-radius: 50%;
    background-color: #545454;
    @include transition(opacity 0.5s);

    &::after {
      display: none;
    }
  }
}

// intl-tel-input
.intlTelInputContainer {
  width: 100%;

  .iti__selected-flag {
    padding: 0;
    margin-right: 15px;
    padding-right: 15px;
    background-color: transparent !important;

    .iti__flag {
      display: none;
    }

    .iti__selected-dial-code {
      font-size: 16px;
      @include semibold();
    }

    &::before {
      right: 0;
      width: 1px;
      content: '';
      height: 15px;
      position: absolute;
      background-color: #dfdfe0;
    }

    .iti__arrow {
      border: none;
      position: relative;

      &::after {
        content: '';
        top: -2px;
        width: 7px;
        right: -7px;
        height: 7px;
        position: absolute;
        background-size: contain;
        background-repeat: no-repeat;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='5.701' height='3.087' viewBox='0 0 5.701 3.087'%3E%3Cpath id='Path_102951' data-name='Path 102951' d='M1063.578,1252.876a.407.407,0,0,1,.573,0l2.16,2.167h0a.81.81,0,0,1,0,1.146h0l-2.178,2.171a.407.407,0,0,1-.573-.573l2-1.993a.252.252,0,0,0,0-.357l-1.981-1.993A.405.405,0,0,1,1063.578,1252.876Z' transform='translate(1258.458 -1063.461) rotate(90)' fill='rgba(0,0,0,0.8)'/%3E%3C/svg%3E%0A");
      }

      &.iti__arrow--up {
        &::after {
          top: -5px;
          @include transform(rotate(180deg));
        }
      }
    }
  }

  .iti__country-name,
  .iti__flag-box {
    font-size: 14px;
    @include medium();
  }
}

.common-black-bg-btn {
  width: 100%;
  color: $white;
  font-size: 16px;
  min-height: 48px;
  border-radius: 8px;
  @include btnReset();
  background-size: contain;
  background-repeat: repeat;
  text-transform: uppercase;
  @include bold();
  background-image: image_url('btn_bg.png');

  @media only screen and (min-width: 1000px) {
    @include moveUp();
  }
}

.common-white-btn {
  @include btnReset();
  width: 100%;
  height: 48px;
  display: block;
  color: $black1;
  font-size: 16px;
  border-radius: 8px;
  background-color: $white;
  border: 1px solid $black1;
  text-transform: uppercase;
  @include bold();
}

.common-black-btn {
  width: 100%;
  height: 48px;
  color: $white;
  display: block;
  font-size: 16px;
  @include btnReset();
  border-radius: 8px;
  background-color: $black1;
  text-transform: uppercase;
  border: 1px solid $black1;
  @include bold();
}

.common-checkbox-grp {
  display: inline-block;
  position: relative;
  padding-left: 30px;
  cursor: pointer;
  font-size: 15px;
  @include medium();

  .l-label {
    @include flc();
    display: inline-block;
  }

  input {
    position: absolute;
    z-index: -1;
    opacity: 0;
  }

  .control-indicator {
    position: absolute;
    top: 2px;
    left: 0;
    height: 18px;
    width: 18px;
    border: 2px solid #00000029;
    background: $white;
    border-radius: 2px;
  }

  &:hover input~.control-indicator,
  input:focus~.control-indicator {
    background: $white;
  }

  input:checked~.control-indicator {
    background: $black;
    border-color: $black;
  }

  &:hover input:not([disabled]):checked~.control-indicator,
  input:checked:focus~.control-indicator {
    background: $black;
  }

  input:disabled~.control-indicator {
    background: #e6e6e6;
    opacity: 0.6;
    pointer-events: none;
  }

  .control-indicator:after {
    content: '';
    position: absolute;
    display: none;
  }

  input:checked~.control-indicator:after {
    display: block;
  }

  .control-indicator:after {
    left: 4px;
    top: 0px;
    width: 6px;
    height: 12px;
    border: solid $white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
  }

  input:disabled~.control-indicator:after {
    border-color: #7b7b7b;
  }

  .colour-label {
    width: 15px;
    height: 15px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 5px;
    margin-top: 5px;
  }
}

.common-radio-btn-grp {
  margin: 15px 0;

  label {
    cursor: pointer;
    display: block;
  }

  input[type='radio'] {
    position: absolute;
    opacity: 0;

    +.radio-label {
      &:before {
        content: '';
        background: $white;
        border-radius: 100%;
        border: 2px solid #00000029;
        display: inline-block;
        width: 20px;
        height: 20px;
        position: relative;
        top: 2px;
        margin-right: 10px;
        vertical-align: top;
        cursor: pointer;
        text-align: center;
        transition: all 250ms ease;
      }
    }

    &:checked {
      +.radio-label {
        &:before {
          background-color: $black;
          box-shadow: inset 0 0 0 3px $white;
        }
      }
    }

    &:focus {
      +.radio-label {
        &:before {
          outline: none;
          border-color: $black;
        }
      }
    }

    &:disabled {
      +.radio-label {
        &:before {
          box-shadow: inset 0 0 0 3px $white;
          border-color: darken($white, 25%);
          background: darken($white, 25%);
        }
      }
    }

    +.radio-label {
      &:empty {
        &:before {
          margin-right: 0;
        }
      }
    }
  }
}

.common-search-box {
  width: 100%;
  border: none;
  color: $black;
  outline: none;
  font-size: 12px;
  border-radius: 8px;
  padding: 4px 10px;
  background: #f0f2f9;
  transition: all 0.2s linear;
  @include medium();
  border: 1.5px solid #f0f2f9;

  &::-webkit-input-placeholder {
    opacity: 0.24;
    color: #000000cc;
  }

  &::-moz-placeholder {
    opacity: 0.24;
    color: #000000cc;
  }

  &:-ms-input-placeholder {
    opacity: 0.24;
    color: #000000cc;
  }

  &:-moz-placeholder {
    opacity: 0.24;
    color: #000000cc;
  }

  &:focus {
    background-color: $white;
    border: 1.5px solid #f0f2f9;
  }
}

.common-float-input-grp {
  width: 100%;
  margin: 15px -14px;
  background-color: transparent;

  .cmn-form-label {
    left: 22px;
    top: 10px;
    padding: 0 4px;
    color: $black1;
    font-size: 16px;
    position: absolute;
    background: $white;
    letter-spacing: 0.15px;
    transition: all 0.3s ease;
    @include medium();
  }

  .cmn-form-input {
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    resize: none;
    color: $black1;
    font-size: 16px;
    position: absolute;
    padding: 10px 15px;
    border: 1px solid $grey;
    background: transparent;
    transition: all 0.3s ease-in-out;
    @include medium();

    &::placeholder {
      opacity: 0;
      visibility: hidden;
      color: transparent;
    }

    &:focus {
      outline: none;
      border: 1px solid $grey;

      &~.cmn-form-label {
        left: 16px;
        z-index: 5;
        top: -8px;
        color: $grey;
        font-size: 12px;
        transition: all 0.3s ease-in-out;
        @include regular();
      }
    }

    &:not(:placeholder-shown).cmn-form-input {
      &:not(:focus) {
        &~.cmn-form-label {
          left: 16px;
          z-index: 5;
          top: -8px;
          color: $grey;
          font-size: 12px;
          transition: all 0.3s ease-in-out;
          @include regular();
        }
      }
    }
  }

  .float-input-wrap {
    width: 100%;
    min-height: 45px;
    position: relative;
    background-color: $white;
  }

  .error-wrap {
    display: none;
    padding: 10px 0 5px 0;

    .err-msg {
      font-size: 16px;
      color: $lightRed;
      letter-spacing: 0.15px;
      @include regular();
    }
  }

  &.show-error-block {
    .cmn-form-label {
      color: $lightRed;
    }

    .cmn-form-input {
      border: 1px solid $lightRed;

      &:focus {
        border: 1px solid $lightRed;

        &~.cmn-form-label {
          color: $lightRed;
        }
      }

      &:not(:placeholder-shown).cmn-form-input {
        &:not(:focus) {
          &~.cmn-form-label {
            color: $lightRed;
          }
        }
      }
    }

    .error-wrap {
      display: block;
    }
  }
}

.common-breadcrumb-wrapper {
  display: none;
  padding: 15px 0 0 0;

  .b-item {
    display: inline;

    &:last-child {
      .b-link {
        color: $black;
      }
    }
  }

  .b-item+.b-item:before {
    padding: 4x;
    color: $lightGrey;
    content: '/\00a0';
  }

  .b-link {
    color: $lightGrey;
    font-size: 14px;
    text-decoration: none;
    text-transform: capitalize;
    @include medium();
  }

  .b-link:hover {
    color: $black;
  }

  @media only screen and (min-width: 1000px) {
    display: block;
  }
}

.common-input-box {
  .i-label {
    color: $grey;
    font-size: 14px;
    @include medium();
  }

  .i-input-control {
    width: 100%;
    border: none;
    outline: none;
    color: $black1;
    font-size: 18px;
    @include semibold();
    background-color: transparent;
    border-bottom: 2px solid #e9ebf0;
    @include iplaceholder();

    &:focus {
      border-bottom: 2px solid $black1;
    }
  }
}

.common-product-info-box {
  .o-heading {
    color: $black1;
    font-size: 16px;
    text-transform: capitalize;
    @include medium();
  }

  .o-des {
    color: $grey;
    font-size: 14px;
    margin: 5px 0;
    display: block;
    line-height: 16px;
    @include medium();
  }

  .amount-box {
    display: block;
    padding-bottom: 4px;
    @include textNowrap();

    .price {
      color: $black1;
      font-size: 16px;
      margin-right: 6px;
      text-transform: capitalize;
      @include semibold();

    }

    .mrp {
      color: $grey;
      font-size: 14px;
      margin-right: 6px;
      position: relative;
      @include medium();

      &::after {
        content: '';
        width: 100%;
        height: 1px;
        position: absolute;
        background-color: $grey;
        @include translateCenter();
      }
    }

    .discount {
      font-size: 14px;
      color: #1daf3a;
      text-transform: uppercase;
      @include medium();
    }
  }

  .o-sku-txt,
  .o-sku-code {
    color: $grey;
    font-size: 12px;
    @include medium();

  }

  .o-variant-size {
    color: $black;
    font-size: 14px;
    @include medium();

  }

  .o-delivers-box {
    display: flex;
    padding-top: 6px;

    svg {
      width: 14px;
    }
  }

  .o-delivers {
    color: $grey;
    font-size: 14px;
    padding-left: 8px;
    @include medium();
  }

  @media only screen and (max-width: 999px) {
    .o-delivers-box {
      padding: 0px 0 2px 0;
      align-items: flex-end;

      svg {
        width: 12px;
      }
    }

    .o-delivers {
      font-size: 10px;
    }
  }
}

.common-size-qty-select {
  &.o-select-flex-container {
    @include flexbox($justify: normal);
  }

  .o-select-box {
    display: flex;
    margin: 10px 0;
    padding: 4px 8px;
    margin-right: 10px;
    border-radius: 6px;
    align-items: center;
    border: 1px solid $black1;

    .o-select-label {
      color: $black1;
      font-size: 12px;
      @include medium();
    }
  }

  .dropdown-select-box {
    display: flex;
    align-items: center;

    .dropdown-menu {
      min-width: 60px;
      background-color: $white;
    }

    .dropdown-item {
      outline: none;
      color: $black1;
      cursor: pointer;
      font-size: 12px;
      background-color: $white;
      text-transform: uppercase;
      @include medium();

      &:hover {
        background-color: #f8faff;
      }
    }

    .dropdown-select-btn {
      color: $black1;
      font-size: 12px;
      min-width: 50px;
      text-align: left;
      @include btnReset();
      padding: 0 20px 0 10px;
      text-transform: uppercase;
      background-color: transparent;
      @include medium();

      &::after {
        position: absolute;
        right: 0;
        top: -3px;
        border: none;
        content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cg id='icon_Right_arrow_small' data-name='icon/Right arrow/small' transform='translate(24) rotate(90)'%3E%3Cpath id='Path_102951' data-name='Path 102951' d='M1063.648,1252.947a.651.651,0,0,1,.918,0l3.459,3.471h0a1.3,1.3,0,0,1,0,1.836h0l-3.489,3.478a.651.651,0,0,1-.918-.918l3.2-3.192a.4.4,0,0,0,0-.571l-3.174-3.193A.65.65,0,0,1,1063.648,1252.947Z' transform='translate(-1053.462 -1245.758)' fill='rgba(0,0,0,0.8)'/%3E%3Crect id='Rectangle_21663' data-name='Rectangle 21663' width='24' height='24' fill='none'/%3E%3C/g%3E%3C/svg%3E%0A");
      }
    }
  }

  @media only screen and (max-width: 999px) {
    .o-select-box {
      padding: 2px 0 2px 8px;
    }

    .dropdown-select-box {
      .dropdown-select-btn {
        font-size: 11px;
        min-width: 40px;
        padding: 0 20px 0 5px;

        &::after {
          top: -5px;
          right: -2px;
          transform: scale(0.8);
        }
      }
    }
  }
}

.common-addres-info-box {
  padding: 24px;
  background: $white;
  border-radius: 8px;
  margin-bottom: 24px;
  box-shadow: -2px 3px 6px #002daa14;

  .a-heading {
    color: $black1;
    font-size: 18px;
    padding-bottom: 10px;
    text-transform: capitalize;
    @include semibold();
  }

  .a-detail {
    color: $black1;
    font-size: 16px;
    display: block;
    @include medium();

  }

  .a-label {
    color: #00a7ef;
    font-size: 10px;
    margin-left: 2px;
    padding: 2px 5px;
    border-radius: 6px;
    text-transform: uppercase;
    background-color: #e3f3ff;
    border: 1px solid #a7dcff;
    @include medium();
  }

  .action-btn {
    @include btnReset();
    width: 100%;
    color: $black1;
    display: block;
    font-size: 14px;
    min-width: 115px;
    padding: 5px 10px;
    font-style: normal;
    margin-bottom: 10px;
    text-transform: uppercase;
    @include medium();
    background-color: transparent;
  }

  .delete-btn {
    color: #f56a6a;
    margin-bottom: 0;
  }

  .action-dropdown-btn-box {
    .act-btn {
      border: none;
      outline: none;
      color: $black1;
      box-shadow: none;
      background-color: transparent;
    }

    .dropdown-menu {
      padding: 10px 8px;
      border-radius: 8px;
      background-color: $white;
    }

    .act-btn:after {
      content: none;
      top: 0;
      left: 0;
      right: 0;
      margin: 0;
      bottom: 0;
      opacity: 0;
      border: none;
      visibility: hidden;
      background-color: rgba(0, 0, 0, 0.7);
      @include transition(opacity 0.15s ease-in-out);
    }

    .act-btn.show:after {
      opacity: 1;
      position: fixed;
      visibility: visible;
      z-index: $zIdxAddressActionBtnOverlay;
    }

    .action-btn {
      padding: 5px 0;
    }

    .edit-btn {
      border-bottom: 1px solid #eeeeee;
    }
  }

  .vertical-btn {
    @include btnReset();
    padding: 10px;
    background-color: transparent;
  }

  &.activeAddress {
    border: 0.5px solid $black1;
  }

  &.new-address-wrap {
    background: #f0f2f9;
    box-shadow: -2px 3px 6px #0000000f;
    background-image: url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' rx='10' ry='10' stroke='%23333' stroke-width='3' stroke-dasharray='7' stroke-dashoffset='0' stroke-linecap='butt'/%3e%3c/svg%3e");
    border-radius: 8px;

    .na-flex-container {
      text-align: center;
      align-items: center;
      justify-content: center;
    }

    .a-new {
      display: block;
      color: $black1;
      font-size: 14px;
      margin-top: 10px;
      text-transform: uppercase;
      @include medium();
    }
  }

  &.address-small {
    box-shadow: none;

    .a-heading {
      font-size: 14px;
    }

    .a-detail {
      color: $black1;
      font-size: 14px;
    }

    .action-dropdown-btn-box .edit-btn {
      padding: 0;
      margin-bottom: 0;
      border-bottom: none;
    }
  }

  @media only screen and (max-width: 999px) {
    padding: 16px;

    .a-heading {
      font-size: 14px;
    }

    .a-detail {
      font-size: 14px;
    }

    .action-dropdown-btn-box {
      .act-btn:after {
        content: '';
      }
    }

    &.new-address-wrap {
      padding: 25px;
      background-color: #f9f9f9;

      .a-new {
        display: block;
        color: $black1;
        font-size: 14px;
        margin-top: 10px;
        text-transform: uppercase;
        @include medium();
      }
    }
  }
}

.accordion-button {
  &::after {
    width: auto;
    background-image: none !important;
    content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cg id='icon_Right_arrow_small' data-name='icon/Right arrow/small' transform='translate(24) rotate(90)'%3E%3Cpath id='Path_102951' data-name='Path 102951' d='M1063.648,1252.947a.651.651,0,0,1,.918,0l3.459,3.471h0a1.3,1.3,0,0,1,0,1.836h0l-3.489,3.478a.651.651,0,0,1-.918-.918l3.2-3.192a.4.4,0,0,0,0-.571l-3.174-3.193A.65.65,0,0,1,1063.648,1252.947Z' transform='translate(-1053.462 -1245.758)' fill='rgba(0,0,0,0.8)'/%3E%3Crect id='Rectangle_21663' data-name='Rectangle 21663' width='24' height='24' fill='none'/%3E%3C/g%3E%3C/svg%3E%0A");
  }
}

.filterCustomScrollbar,
.defaultCustomScrollbar {
  .mCSB_scrollTools {
    width: 5px !important;
  }

  .mCSB_scrollTools {
    background-color: #f0f2f9;
    opacity: 1 !important;
  }

  .mCSB_scrollTools_vertical {
    .mCSB_draggerContainer {
      border-radius: 10px;
      overflow: hidden;

      .mCSB_dragger {
        border-radius: 10px;
        overflow: hidden;

        .mCSB_dragger_bar {
          width: 100% !important;
          background-color: #00443d;
          border-radius: 2px !important;
        }
      }
    }
  }

  .mCSB_draggerRail {
    width: 100% !important;
    background-color: #f0f2f9;
    border-radius: 0 !important;
  }
}

.product-card-col {
  @media only screen and (max-width: 576px) {
    &:nth-child(even) .common-product-card-wrapper {
      margin: 0 0 0 auto;
    }
  }
}

.pagination {
  display: flex;
  padding-left: 0;
  list-style: none;
  justify-content: center;

  span.last,
  span.first {
    display: none;
  }

  span.page {
    padding: 0 6px;
  }

  span.page.current {
    background: #00443d;
    color: #fff;
    border-radius: 100%;
  }
}

.common-product-card-wrapper {
  width: 268px;
  height: 500px;
  display: block;
  overflow: hidden;
  margin-bottom: 30px;

  &:hover .p-overlay {
    height: 120px;
  }

  .p-title {
    color: $black1;
    display: block;
    font-size: 16px;
    text-transform: capitalize;
    @include textNowrap();
    @include semibold();
    font-family: 'Cormorant Garamond' !important;
  }

  .p-des {
    color: $grey;
    display: block;
    @include flc();
    font-size: 12px;
    overflow: hidden;
    font-weight: 400;
    font-style: normal;
    line-height: 2em;
    height: 2em;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
  }

  .p-amount-box {
    display: block;
    @include textNowrap();

    .p-price {
      font-size: 16px;
      color: $black1;
      text-transform: capitalize;
      margin-right: 5px;
      @include semibold();


      .currency-icon {
        font-size: 16px;
      }
    }

    .p-mrp {
      color: $grey;
      font-size: 13px;
      margin-right: 5px;
      @include medium();
      position: relative;

      &::after {
        content: '';
        width: 100%;
        height: 1px;
        position: absolute;
        background-color: $grey;
        @include translateCenter();
      }
    }

    .p-discount {
      font-size: 11px;
      color: #1daf3a;
      margin-right: 5px;
      @include medium();
    }
  }

  .p-img-box {
    height: 400px;
    position: relative;

    .p-img {
      @include fitCover();
    }
  }

  .p-overlay {
    bottom: 0;
    height: 0;
    width: 100%;
    padding-top: 0;
    padding: 0 10px;
    overflow: hidden;
    position: absolute;
    background-color: rgba(255, 255, 255, 0.8);
    -webkit-transition: 0.3s ease;
    -o-transition: 0.3s ease;
    transition: 0.3s ease;
  }

  .p-detail-box {
    padding-top: 10px;
  }

  .p-txt-box {
    margin-top: 5px;
  }

  .p-quick-txt {
    font-size: 13px;
    text-transform: uppercase;
    @include semibold();
  }

  .p-size-box {
    margin: 10px 0 15px 0;
    @include flexbox($wrap: nowrap);
  }

  .p-size-btn,
  .p-wishlist-btn,
  .p-share-btn {
    height: 25px;
    color: $black1;
    min-width: 25px;
    font-size: 12px;
    padding: 0px 5px;
    overflow: hidden;
    line-height: 25px;
    border-radius: 4px;
    @include btnReset();
    background-color: $white;
    text-transform: uppercase;
    @include medium();
    @include flexbox($wrap: nowrap, $justify: center);
  }

  .p-add-cart-btn {
    padding: 0;
    height: 30px;
    font-size: 12px;
    color: $black1;
    border-radius: 8px;
    padding: 5px 40px;
    @include btnReset();
    background-color: $white;
    text-transform: uppercase;
    @include bold();

    &:hover {
      color: $white;
      background-color: $black1;
    }
  }

  .p-wishlist-btn,
  .p-share-btn {
    svg {
      width: 15px;
      height: 15px;
    }
  }

  .p-size-btn {
    &.size-selected {
      color: $white;
      background-color: $black1;
    }

    &:hover {
      color: $white;
      background-color: $black1;
    }
  }

  .p-wishlist-btn {
    &:hover {
      svg path {
        fill: $black1;
      }
    }

    &.p-wishlisted {
      svg path {
        fill: $black1;
      }
    }
  }

  .p-share-cart-box {
    @include flexbox($wrap: nowrap);

    svg {
      background-color: $white;
    }
  }

  .mg-l {
    margin-left: 10px;
  }

  .p-label {
    color: $white;
    @include semibold();
    font-size: 10px;
    position: absolute;
    top: 0px;
    padding: 2px 5px;

    &.black {
      background-color: $black1;
      left: 0px;
    }

    &.red {
      background-color: #8c1823;
      right: 0px;
    }
  }

  @media only screen and (max-width: 999px) {
    width: 100%;
    height: 100%;
    margin-bottom: 0;

    .p-img-box {
      height: 280px;
      overflow: hidden;
    }

    .p-detail-box {
      padding: 8px;
    }

    .p-title {
      font-size: 15px;
    }

    .p-des {
      font-size: 13px;
    }

    .p-overlay {
      height: auto;
      position: static;
      background: transparent;
    }

    .p-txt-box,
    .p-size-box,
    .p-add-cart-btn,
    .p-share-btn {
      display: none;
    }

    .p-wishlist-btn {
      right: 15px;
      width: 26px;
      bottom: 15px;
      height: 26px;
      position: absolute;
      border-radius: 6px;
      border: 1px solid $lightGrey;

      svg {
        width: 15px;
        height: 25px;
      }
    }

    .p-amount-box {
      white-space: nowrap;

      .p-price {
        font-size: 13px;
        margin-right: 3px;

        .currency-icon {
          font-size: 11px;
        }
      }

      .p-mrp {
        font-size: 13px;
        margin-right: 3px;

        .currency-icon {
          font-size: 10px;
        }
      }

      .p-discount {
        font-size: 11px;
        margin-right: 3px;
      }
    }
  }
}

.wishlist-product {
  margin-bottom: 20px;

  .common-product-card-wrapper {
    margin-bottom: 0 !important;
    height: unset !important;
  }

  .wihlist-add-to-cart {
    background: #00443d;
    color: #fff;
    padding: 10px 18px;
    cursor: pointer;
    width: 100%;
    max-width: 266px;
    margin: 10px 0;
    border-radius: 5px;
    text-align: center;
  }
}

.common-product-v2-slider-wrapper {
  margin: 60px 0;

  .heading {
    color: $black1;
    font-size: 28px;
    margin-bottom: 24px;
    padding-left: 40px;
    text-transform: uppercase;
    @include semibold();
  }

  .a-lnik {
    color: $black1;
    padding-right: 40px;
    font-size: 20px;
    margin-bottom: 24px;
    text-transform: uppercase;
    @include semibold();
  }

  .product-swiper {
    padding: 0 40px;

    &:hover {

      .product-prev-btn,
      .product-next-btn {
        opacity: 1;

        &.swiper-button-disabled {
          opacity: 0.35;
        }
      }
    }
  }

  .product-prev-btn,
  .product-next-btn {
    opacity: 0;
    width: 40px;
    height: 40px;
    color: $white;
    border-radius: 50%;
    background-color: #545454;
    -webkit-transition: opacity 0.5s;
    -o-transition: opacity 0.5s;
    transition: opacity 0.5s;

    &::after {
      display: none;
    }
  }

  &.hide-action-btn {
    .action-btn-grp {
      display: none;
    }
  }

  @media only screen and (max-width: 999px) {
    margin: 20px 0;
    padding: 30px 0 0;

    .heading {
      font-size: 20px;
      padding-left: 0;
      margin-bottom: 15px;
      text-transform: lowercase;
      @include flc();
    }

    .see-all-link-box {
      display: none;
    }

    .product-swiper {
      padding: 0 12px;
    }

    .product-prev-btn,
    .product-next-btn {
      display: none;
    }
  }
}

.common-product-card-v2-wrapper {
  width: 273px;
  display: block;

  .p-img-box {
    width: 273px;
    height: 400px;
  }

  .p-card-img {
    @include fitCover();
  }

  .p-txt-box {
    padding: 15px 0;
  }

  .p-card-name {
    color: $black1;
    font-size: 20px;
    overflow: hidden;
    margin-bottom: 5px;
    white-space: nowrap;
    text-overflow: ellipsis;
    text-transform: capitalize;
    @include semibold();
  }

  .p-card-des {
    color: $grey;
    font-size: 16px;
    overflow: hidden;
    white-space: nowrap;
    margin-bottom: 10px;
    text-overflow: ellipsis;
    @include regular();
  }

  .amount-box {
    display: block;
    margin-bottom: 10px;
    white-space: nowrap;
    @include textNowrap();

    .price {
      color: $black1;
      font-size: 20px;
      margin-right: 6px;
      text-transform: capitalize;
      @include semibold();


      .currency-icon {
        color: $black1;
        font-size: 18px;
      }
    }

    .mrp {
      color: $grey;
      font-size: 18px;
      margin-right: 6px;
      position: relative;
      @include medium();

      &::after {
        content: '';
        width: 100%;
        height: 1px;
        position: absolute;
        background-color: $grey;
        @include translateCenter();
      }

      .currency-icon {
        color: $grey;
        font-size: 14px;
      }
    }

    .discount {
      font-size: 13px;
      color: #1daf3a;
      text-transform: uppercase;
      @include medium();
    }
  }

  .currency-icon {
    font-size: initial;
  }

  .action-btn-grp {
    display: flex;
  }

  .p-wishlist-btn {
    padding: 0;
    width: 48px;
    height: 48px;
    font-size: 14px;
    color: $black1;
    overflow: hidden;
    border-radius: 8px;
    @include btnReset();
    border: 1px solid $black1;
    background-color: $white;
    text-transform: uppercase;
    @include medium();

    &:hover {
      svg path {
        fill: $black1;
      }
    }

    &.p-wishlisted {
      svg path {
        fill: $black1;
      }
    }
  }

  .p-card-btn {
    flex: 1;
    margin-right: 12px;
    @include moveUp();
    @include commonBuyBtn($width: 100%, $fs: 16px);
  }

  @media only screen and (max-width: 999px) {
    width: 156px;
    display: block;

    .p-img-box {
      width: 156px;
      height: 200px;
    }

    .p-txt-box {
      padding: 10px 0;
    }

    .p-card-name {
      font-size: 14px;
      margin-bottom: 0;
    }

    .p-card-des {
      font-size: 14px;
      margin-bottom: 0;
    }

    .amount-box {
      margin-bottom: 10px;

      .price {
        font-size: 16px;
        margin-right: 2px;

        .currency-icon {
          font-size: 12px;
        }
      }

      .mrp {
        font-size: 12px;
        margin-right: 2px;

        .currency-icon {
          font-size: 8px;
        }
      }

      .discount {
        font-size: 10px;
      }
    }

    .action-btn-grp {
      display: none;
    }
  }
}

/* Qucik View Modal */
.quick-view-produc-modal-wrapper {
  .modal-dialog {
    max-width: 993px;
  }

  .modal-body {
    padding: 60px 60px 25px 60px;
  }

  .flex-container {
    @include flexbox($wrap: nowrap, $justify: normal, $align: normal);

    .flex-item-1 {
      width: 369px;
    }

    .flex-item-2 {
      flex: 1;
      padding-left: 20px;
    }
  }

  .slider-img {
    @include fitCover();
  }

  .p-title {
    font-size: 28px;
    color: $black;
    display: block;
    @include semibold();
  }

  .p-des {
    font-size: 12px;
    display: block;
    color: $black;
  }

  .p-code {
    font-size: 16px;
    display: block;
    color: #707070;
  }

  .p-amount-box {
    display: block;

    .p-price {
      font-size: 24px;
      color: $black1;
      margin-right: 10px;
      text-transform: capitalize;
      @include bold();

      .currency-icon {
        color: $black1;
        font-size: 18px;
      }
    }

    .p-mrp {
      color: $grey;
      font-size: 24px;
      margin-right: 10px;
      @include medium();
      position: relative;

      &::after {
        content: '';
        width: 100%;
        height: 1px;
        position: absolute;
        background-color: $grey;
        @include translateCenter();
      }

      .currency-icon {
        color: $grey;
        font-size: 18px;
      }
    }

    .p-discount {
      font-size: 16px;
      color: #1daf3a;
      margin-right: 10px;
      @include medium();
    }
  }

  .wallet-offer-txt {
    color: #0000007a;
    font-size: 16px;

    .amount-tx {
      color: $black;
    }
  }

  .select-size-wrapper {
    padding: 30px 0 20px;
    position: relative;

    .chart-scale-box {
      top: 30px;
      right: 130px;
      display: flex;
      cursor: pointer;
      position: absolute;
      align-items: center;

      .c-txt {
        margin-left: 5px;
      }
    }

    .ss-txt {
      color: $black;
      font-size: 16px;
      @include semibold();
    }

    .s-size-box-wrap {
      margin-top: 20px;
    }

    .s-size-box {
      text-align: center;
      display: inline-block;
      min-width: 40px;
      min-height: 40px;
      border: 1px solid $black1;
      padding: 10px 2px;
      font-size: 12px;
      cursor: pointer;
      margin-right: 20px;
      text-transform: uppercase;

      &.s-selected {
        color: $white;
        background-color: $black1;
      }

      &:hover {
        color: $white;
        background-color: $black1;
      }
    }
  }

  .p-size-btn,
  .p-wishlist-btn,
  .p-share-btn {
    padding: 0;
    width: 48px;
    height: 48px;
    font-size: 14px;
    color: $black1;
    overflow: hidden;
    border-radius: 8px;
    @include btnReset();
    border: 1px solid $black1;
    background-color: $white;
    margin-right: 20px;
    text-transform: uppercase;
    @include medium();
  }

  .p-add-cart-btn {
    padding: 0;
    width: 328px;
    height: 48px;
    font-size: 16px;
    @include btnReset();
    padding: 5px 33px;
    border-radius: 8px;
    margin-right: 20px;
    border: 1px solid $black1;
    background-color: $white;
    text-transform: uppercase;
    @include bold();
    letter-spacing: 1px;
  }

  .p-wishlist-btn {
    &:hover {
      svg path {
        fill: $black1;
      }
    }

    &.p-wishlisted {
      svg path {
        fill: $black1;
      }
    }
  }

  .p-share-cart-box {
    padding: 30px 0;
    @include flexbox($wrap: nowrap, $justify: normal);

    svg {
      background-color: $white;
    }
  }

  .view-detail-link-wrapper {
    text-align: center;
    padding: 30px;

    .a-link {
      font-size: 20px;
      @include semibold();
      text-transform: uppercase;
    }

    .icon-ar {
      font-size: 15px;
      margin-left: 10px;
    }
  }

  .quickViewModalSwiperSlider {
    .swiper-wrapper {
      padding-bottom: 50px;
    }

    .swiper-pagination-bullet {
      width: 20px;
      height: 7px;
      display: inline-block;
      margin: 0 2px;
      border-radius: 12px;
      background-color: $grey;
      position: relative;
      vertical-align: middle;
      text-align: center;
      transition: all 0.3s ease-out;

      &.swiper-pagination-bullet-active {
        opacity: 1;
        width: 25px;
      }
    }
  }
}

.item-added-to-cart-toast-wrapper {
  left: 0;
  right: 0;
  z-index: 10;
  bottom: 70px;
  position: fixed;
  @include flexbox($justify: center);

  .iac-toast-body {
    color: $white;
    background-color: $black1;
  }
}

#sizeMeasureChartModal {
  .modal-body {
    padding: 0;
  }
}

#modal-size-chart {
  .modal-content {
    width: 95%;
    border: none;
    padding: 0;

  }

  .modal-sm-size {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .modal-body {
    padding: 0;
  }

  .table-inches {
    width: 100%;
  }

  .std_size_chart {
    width: 100%;
  }
}


.address-added-successfully-toast-wrapper {
  left: 0;
  right: 0;
  z-index: 10;
  bottom: 70px;
  position: fixed;
  @include flexbox($justify: center);

  .iac-toast-body {
    color: $white;
    background-color: $black1;
  }
}

.error-toast-wrapper {
  left: 0;
  right: 0;
  z-index: 10;
  bottom: 80px;
  position: fixed;
  @include flexbox($justify: center);

  .error-toast-body {
    border: none;
    box-shadow: none;
    border-radius: 6px;
    background-color: $lightRed;
  }

  .error-toast-msg {
    font-size: 16px;
    color: $white;
    @include medium();
  }
}

.mobile-share-whatsapp-wrapper {
  right: 20px;
  bottom: 80px;
  display: block;
  position: fixed;
  z-index: $zIdxWhatsappBtn;

  .btn-share-whatsapp {
    @include btnReset();
    background-color: transparent;
    width: 120px;
  }

  @media only screen and (max-width: 999px) {
    display: block;
  }
}

.size-measure-chart-modal-wrapper {
  .modal-dialog {
    min-width: 1000px;
  }

  .cs-modal-header {
    display: none;
  }

  .s-flex-container {
    @include flexbox($align: normal);

    .s-flex-item {
      width: 100%;
    }
  }

  .one {
    border-right: 8px solid #f0f2f9;

    .detail-box {
      padding-right: 20px;
    }
  }

  .two {
    .detail-box {
      padding-left: 20px;
    }
  }

  .heading {
    color: $black;
    font-size: 20px;
    padding-left: 34px;
    padding-top: 16px;
    @include bold();
  }

  .size-measure-img {
    max-width: 95%;
    margin: 0 auto;
    display: block;
  }

  .size-table {
    margin: 15px 0;

    table {
      width: 100%;
      border-collapse: collapse;
    }

    td,
    th {
      font-size: 12px;
      text-align: left;
      padding: 5px 10px;
      text-transform: uppercase;
      border: 1px solid #f0f0f0;
    }

    th {
      text-transform: capitalize;
    }

    tr:nth-child(even) {
      background-color: #e2e2e2;
    }

    table,
    td {
      @include semibold();
    }
  }

  @media only screen and (max-width: 999px) {
    .modal-dialog {
      margin: 0;
      min-width: 100%;
      min-height: 100%;
      background-color: $white;
    }

    .modal-content {
      border: none;
      box-shadow: none;
      border-radius: 0;
    }

    .modal-body {
      padding: 0;
    }

    .cs-modal-header {
      display: block;
    }

    .s-flex-container {
      padding-top: 45px;

      .s-flex-item {
        width: 100%;
        padding: 15px;
      }
    }

    .one {
      border-right: none;
      border-bottom: 8px solid #f0f2f9;

      .detail-box {
        padding-right: 0;
      }
    }

    .two {
      .detail-box {
        padding-left: 0;
      }
    }

    .size-table {
      margin: 0;
    }

    .size-measure-img {
      max-width: 95%;
    }
  }
}

.order-filter-modal-wrapper {
  @include modalBottomDrawer();

  .modal-body {
    padding: 0;
    padding-bottom: 80px;
  }

  .modal-dialog {
    margin: 0;
    height: 100%;
    min-width: 100%;
    min-height: 100%;
    background-color: $white;
  }

  .modal-content {
    border: none;
    box-shadow: none;
    border-radius: 0;
  }

  .filter-list-wrapper {
    padding: 16px;

    &.first {
      margin-top: 60px;
    }

    &.second {
      padding-top: 30px;
      border-top: 8px solid #f0f2f9;
    }

    .heading {
      color: $black;
      font-size: 20px;
      margin-bottom: 25px;
      @include semibold();
    }
  }

  .common-radio-btn-grp label {
    text-transform: uppercase;
    font-size: 14px;
  }

  .radio-item {
    margin: 20px 0;
  }

  .clear-apply-btn-bottom-wrapper {
    bottom: 0;
    width: 100%;
    position: fixed;
    max-width: 1366px;
    background-color: $white;
    border-top: 1px solid #eaeaec;

    .flex-container {
      padding: 15px;
      text-align: center;
      @include flexbox($align: flex-start);

      .flex-item {
        width: 48%;
      }
    }
  }
}

.logout-modal-wrapper {
  .modal-dialog {
    max-width: 520px;
  }

  .modal-content {
    border-radius: 10px;
  }

  .modal-body {
    padding: 50px 70px;
    text-align: center;
  }

  .flex-container {
    display: flex;
    justify-content: space-between;
  }

  .heading {
    color: $black;
    font-size: 28px;
    padding-bottom: 50px;
    @include semibold();
  }

  .action-btn {
    min-width: 160px;
  }

  @media only screen and (max-width: 999px) {
    @include modalBottomDrawer();

    .modal-body {
      padding: 20px;
      text-align: left;
    }

    .modal-dialog {
      margin: 0;
      min-width: 100%;
      background-color: $white;
    }

    .heading {
      font-size: 20px;
      padding-bottom: 20px;
    }

    .flex-container {
      .flex-item {
        width: 48%;
      }
    }

    .action-btn {
      width: 100%;
      min-width: auto;
    }
  }
}

.delete-address-modal-wrapper {
  .modal-dialog {
    max-width: 520px;
  }

  .modal-content {
    border-radius: 10px;
  }

  .modal-body {
    padding: 50px;
    text-align: center;
  }

  .flex-container {
    display: flex;
    justify-content: space-between;
  }

  .heading {
    color: $black;
    font-size: 28px;
    line-height: 35px;
    padding-bottom: 50px;
    @include semibold();
  }

  .footer-btn-wrapper {
    max-width: 350px;
    margin: 0 auto;
  }

  .action-btn {
    min-width: 160px;
  }

  @media only screen and (max-width: 999px) {
    @include modalBottomDrawer();

    .modal-body {
      padding: 20px;
    }

    .heading {
      font-size: 20px;
      text-align: left;
      line-height: 25px;
      padding-bottom: 20px;
    }

    .flex-item {
      width: 48%;
    }

    .action-btn {
      min-width: auto;
    }
  }
}

.cart-remove-item-modal-wrapper {
  .modal-dialog {
    max-width: 520px;
  }

  .modal-content {
    border-radius: 10px;
    border-top-left-radius: 16px !important;
    border-top-right-radius: 16px !important;
    border-bottom-left-radius: 16px !important;
    border-bottom-right-radius: 16px !important;
  }

  .modal-body {
    padding: 30px;
    text-align: center;
    height: 12rem;
  }

  .heading {
    color: $black;
    font-size: 28px;
    line-height: 35px;
    padding-bottom: 30px;
    @include semibold();
  }

  .footer-btn-wrapper {
    max-width: 350px;
    margin: 0 auto;

    .flex-container {
      display: flex;
      justify-content: space-between;
    }
  }

  .action-btn {
    min-width: 150px;
    height: 38px;
    font-size: 13px;
  }

  @media only screen and (max-width: 999px) {
    @include modalBottomDrawer();

    .modal-body {
      padding: 20px;
      height: 10rem;
    }

    .heading {
      font-size: 20px;
      text-align: left;
      line-height: 25px;
      padding-bottom: 20px;
    }

    .flex-item {
      width: 48%;
    }
  }
}

.feedback-modal-wrapper {
  .modal-dialog {
    max-width: 520px;
  }

  .modal-content {
    border-radius: 10px;
  }

  .modal-body {
    padding: 50px 70px;
    text-align: center;
  }

  .heading {
    color: $black;
    font-size: 28px;
    padding-bottom: 50px;
    @include semibold();
  }

  .rating-stars {
    .start-list {
      list-style-type: none;
      padding: 0;
    }

    .start-list {
      list-style-type: none;
      padding: 0;
    }

    .start-list>.star {
      display: inline-block;
      margin-right: 30px;

      &:last-child {
        margin-right: 0px;
      }
    }

    .start-list>.star>i.fa {
      font-size: 24px;
      cursor: pointer;
      color: #cccccc;
    }

    .start-list>.star.hover>i.fa {
      color: #ffcc36;
    }

    .start-list>.star.selected>i.fa {
      color: #ffb52f;
    }
  }
}

.order-cancel-select-reason-modal-wrapper {
  .modal-dialog {
    max-width: 570px;
  }

  .modal-content {
    border-radius: 8px;
  }

  .modal-body {
    padding: 48px;
  }

  .heading {
    color: $black;
    font-size: 28px;
    @include semibold();
  }

  .select-reason-options-list {
    padding: 20px 0 30px 0;
  }

  .next-btn {
    max-width: 190px;
  }

  .common-radio-btn-grp {
    position: relative;

    label {
      font-size: 14px;
    }

    input[type='radio'] {
      right: 0;

      +.radio-label {
        &:before {
          position: absolute;
          right: 0;
        }
      }
    }
  }

  @media only screen and (max-width: 999px) {
    @include modalBottomDrawer();

    .modal-body {
      padding: 0;
      padding-bottom: 80px;
    }

    .modal-dialog {
      margin: 0;
      min-width: 100%;
      background-color: $white;
    }

    .modal-content {
      border: none;
      box-shadow: none;
      border-radius: 0;
    }

    .heading {
      padding: 20px;
      font-size: 20px;
      padding-bottom: 0;
    }

    .select-reason-options-list {
      padding: 20px;
      padding-top: 0;
    }

    .next-btn-wrapper {
      padding: 20px;

      .next-btn {
        max-width: 100%;
      }
    }

    .m-bottom-fixed {
      bottom: 0;
      width: 100%;
      position: fixed;
      padding-top: 15px;
      background: $white;
      padding-bottom: 15px;
      z-index: $zIdxModalBottomBtn;
    }
  }
}

.grant-location-access-modal-wrapper {
  .modal-dialog {
    max-width: 570px;
  }

  .modal-content {
    border-radius: 8px;
  }

  .modal-body {
    padding: 48px;
    text-align: center;
  }

  .heading {
    color: $black;
    margin: 20px 0;
    font-size: 28px;
    @include semibold();
  }

  .sub-heading {
    color: $grey;
    font-size: 16px;
    display: block;
    margin-bottom: 20px;
    line-height: 20px;
    @include medium();
  }

  .location-img {
    max-width: 130px;
    display: block;
    margin: 0 auto;
  }

  .next-btn {
    max-width: 190px;
  }

  .location-access-btn {
    max-width: 330px;
  }

  @media only screen and (max-width: 999px) {
    @include modalBottomDrawer();

    .modal-body {
      padding: 0;
      text-align: left;
      padding-top: 20px;
      padding-bottom: 80px;
    }

    .modal-dialog {
      margin: 0;
      min-width: 100%;
      background-color: $white;
      align-items: flex-end;
    }

    .modal-content {
      border: none;
      box-shadow: none;
      border-radius: 0;
    }

    .heading {
      font-size: 20px;
      margin-bottom: 0;
      padding: 16px 16px 0 16px;
    }

    .sub-heading {
      padding: 16px;
      font-size: 14px;
      margin-bottom: 0;
      @include regular();
    }

    .next-btn-wrapper {
      padding: 20px;

      .location-access-btn {
        max-width: 100%;
      }
    }

    .m-bottom-fixed {
      bottom: 0;
      width: 100%;
      position: fixed;
      padding-top: 15px;
      background: $white;
      padding-bottom: 15px;
      z-index: $zIdxModalBottomBtn;
    }
  }
}

.cancel-order-confirmation-modal-wrapper {
  .modal-dialog {
    max-width: 570px;
  }

  .modal-content {
    border-radius: 8px;
  }

  .modal-body {
    padding: 48px;
  }

  .heading {
    color: $black;
    font-size: 24px;
    @include semibold();
  }

  .heading-two {
    color: $black1;
    font-size: 20px;
    @include semibold();
  }

  .o-info {
    color: $grey;
    font-size: 14px;
    @include medium();
  }

  .o-reason-txt {
    color: $black1;
    font-size: 14px;
    @include medium();
  }

  .dustbin-icon {
    text-align: center;
    padding-bottom: 50px;
  }

  .small-container {
    max-width: 380px;
    padding-top: 20px;
  }

  .pd-vertical {
    padding: 5px 0;
  }

  .pd-top {
    padding-top: 15px;
  }

  .action-btn-wrapper {
    display: flex;
    padding: 30px 0 0 0;
    justify-content: space-between;

    .action-btn {
      max-width: 160px;
    }

    .yes-btn[disabled='disabled'],
    .yes-btn:disabled {
      cursor: not-allowed;
      border-color: #d9d9d9;
      background-color: #d9d9d9;
    }
  }

  .error-block {
    background-color: $black1;
    border-radius: 4px;
    margin: 35px 0 5px 0;
    padding: 12px 16px;
    display: none;

    &.showErrorBlock {
      display: block;
    }

    .error-msg {
      color: $white;
      display: block;
      font-size: 14px;
      @include medium();
    }
  }

  @media only screen and (max-width: 999px) {
    .modal-body {
      padding: 70px 0 80px 0;
    }

    .modal-dialog {
      margin: 0;
      min-width: 100%;
      min-height: 100%;
      align-items: normal;
      background-color: $white;
    }

    .modal-content {
      border: none;
      box-shadow: none;
      border-radius: 0;
    }

    .c-modal-content-wrap {
      padding: 16px;
    }

    .m-bottom-fixed {
      bottom: 0;
      width: 100%;
      left: 0;
      position: fixed;
      padding: 15px;
      background: $white;
      padding-bottom: 15px;
      z-index: $zIdxModalBottomBtn;
      box-shadow: 0px -3px 6px #0000000a;
    }

    .heading {
      font-size: 16px;
      @include medium();
    }

    .heading-two {
      font-size: 16px;
      @include semibold();
    }

    .o-reason-txt {
      color: $black1;
      font-size: 14px;
      @include medium();
    }

    .action-btn-wrapper .action-btn {
      max-width: 48%;
    }

    .error-block {
      width: 100%;
      padding: 12px;
      position: absolute;
      bottom: 80px;
      left: 0;
      right: 0;
      margin: 5px 0;
      background-color: transparent;

      .error-msg {
        background-color: $black1;
        border-radius: 4px;
        padding: 12px 16px;
      }
    }
  }
}

.register-login-modal-wrapper {
  .modal-dialog {
    max-width: 444px;
  }

  .modal-content {
    border-radius: 10px;
  }

  .modal-body {
    padding: 48px 36px;
  }

  .heading {
    color: $black;
    font-size: 28px;
    padding-bottom: 10px;
    @include semibold();
  }

  .input-flex-container {
    width: 100%;
    padding: 25px 0;
    @include flexbox($align: normal, $wrap: nowrap, $justify: normal);

    .i-flex-item-1 {
      width: 35%;
    }

    .i-flex-item-2 {
      width: 65%;
    }
  }

  .action-btn {
    margin: 25px 0;
    @include semibold();
  }

  .sub-txt {
    color: $grey;
    display: block;
    font-size: 14px;
    text-align: center;
    @include medium();
  }

  .social-btn-wrap {
    margin: 15px auto 20px auto;
    @include flexbox();

    .social-btn {
      @include btnReset();
      background-color: transparent;
    }
  }

  .already-acct-txt {
    color: $grey;
    display: block;
    font-size: 16px;
    text-align: center;
    @include medium();
  }

  .login-txt {
    color: $black1;
    font-size: 14px;
    cursor: pointer;
    margin-left: 6px;
    @include semibold();
    text-decoration: underline;
  }

  .forgot-pass-txt {
    color: $black1;
    font-size: 14px;
    cursor: pointer;
    margin-left: 6px;
    display: block;
    text-align: right;
    text-transform: uppercase;
    @include semibold();
  }

  @media only screen and (max-width: 999px) {
    .modal-body {
      padding: 40px 10px;
    }
  }
}

.change-password-modal-wrapper {
  .modal-dialog {
    max-width: 444px;
  }

  .modal-content {
    border-radius: 10px;
  }

  .modal-body {
    padding: 48px 36px;
  }

  .heading {
    color: $black;
    font-size: 28px;
    padding-bottom: 10px;
    @include semibold();
  }

  .input-flex-container {
    width: 100%;
    padding: 25px 0;
    @include flexbox($align: normal, $wrap: nowrap, $justify: normal);

    .i-flex-item-1 {
      width: 45%;
    }

    .i-flex-item-2 {
      width: 55%;
    }

    .i-label {
      color: $grey;
      font-size: 14px;
      @include medium();
    }

    .i-input-control {
      width: 100%;
      border: none;
      outline: none;
      color: $black1;
      font-size: 18px;
      @include semibold();
      background-color: transparent;
      border-bottom: 2px solid #e9ebf0;
      @include iplaceholder();

      &:focus {
        border-bottom: 2px solid $black1;
      }
    }

    .otp-code-inputs {
      display: flex;

      .otp-input {
        width: 20px;
        border: none;
        outline: none;
        color: $black;
        font-size: 18px;
        margin-right: 11px;
        border-bottom: 2px solid #e9ebf0;
        @include semibold();

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }

  .action-btn {
    margin-top: 25px;
    @include semibold();
  }

  @media only screen and (max-width: 999px) {
    .modal-body {
      padding: 40px 10px;
    }
  }
}

.cart-drawer-component-container {
  $drawerWidth: 410px;
  top: 0;
  right: 0;
  height: 100%;
  position: fixed;
  overflow-x: hidden;
  width: $drawerWidth;
  background-color: $white;
  @include transform(translate(500px, 0));
  z-index: $zIdxCartRightSidenavDrawer;
  @include transition(transform 0.3s ease-in-out);

  .header-box {
    padding: 16px;

    .flex-container {
      position: relative;
      @include flexbox($justify: normal, $wrap: nowrap);
    }

    .action-btn {
      padding: 0;
      border: none;
      outline: none;
      padding-right: 8px;
      background-color: transparent;
    }

    .heading {
      color: $black;
      font-size: 20px;
      padding-left: 10px;
      @include semibold();
    }

    .count {
      color: $grey;
      font-size: 12px;
      @include medium();
    }
  }

  .drawer-content-box {
    overflow: auto;
    height: calc(100vh - 146px);
  }

  .order-list-box {
    padding: 16px;
  }

  .o-card-item {
    padding: 16px;
    margin: 16px 0;
    background-color: $white;
    border: 2px solid #f0f2f9;
    @include flexbox($align: normal, $wrap: nowrap);

    &:nth-child(1) {
      margin-top: 0;
    }

    &:last-child {
      margin-bottom: 0;
    }

    .pd-around {
      padding: 0 8px;
    }

    .flex-item-one {
      width: 90px;
      height: 115px;
    }

    .flex-item-two {
      flex: 1;
      @include textCut();
    }

    .flex-item-three {
      width: 25%;
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      align-content: center;
    }

    .o-img {
      display: block;
      @include fitCover();
    }

    .o-select-flex-container {
      @include flexbox($justify: normal);
    }

    .o-select-box {
      display: flex;
      margin: 10px 0;
      padding: 2px 4px;
      margin-right: 10px;
      border-radius: 6px;
      align-items: center;
      border: 1px solid $black1;

      .o-select-label {
        color: $black1;
        font-size: 12px;
        @include medium();
      }
    }

    .dropdown-select-box {
      display: flex;
      align-items: center;

      .dropdown-menu {
        min-width: 60px;
        background-color: $white;
      }

      .dropdown-item {
        outline: none;
        color: $black1;
        cursor: pointer;
        font-size: 12px;
        background-color: $white;
        text-transform: uppercase;
        @include medium();

        &:hover {
          background-color: #f8faff;
        }
      }

      .dropdown-select-btn {
        color: $black1;
        font-size: 12px;
        min-width: 40px;
        text-align: left;
        @include btnReset();
        padding: 0 17px 0 5px;
        text-transform: uppercase;
        background-color: transparent;
        @include medium();

        &::after {
          position: absolute;
          right: 0;
          top: -3px;
          border: none;
          content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cg id='icon_Right_arrow_small' data-name='icon/Right arrow/small' transform='translate(24) rotate(90)'%3E%3Cpath id='Path_102951' data-name='Path 102951' d='M1063.648,1252.947a.651.651,0,0,1,.918,0l3.459,3.471h0a1.3,1.3,0,0,1,0,1.836h0l-3.489,3.478a.651.651,0,0,1-.918-.918l3.2-3.192a.4.4,0,0,0,0-.571l-3.174-3.193A.65.65,0,0,1,1063.648,1252.947Z' transform='translate(-1053.462 -1245.758)' fill='rgba(0,0,0,0.8)'/%3E%3Crect id='Rectangle_21663' data-name='Rectangle 21663' width='24' height='24' fill='none'/%3E%3C/g%3E%3C/svg%3E%0A");
        }
      }
    }

    .action-btn {
      height: 37px;
      font-size: 14px;
      margin-bottom: 15px;
      @include moveUp();

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .price-details-box {
    padding: 16px;
    border-top: 8px solid #f0f2f9;

    .heading {
      color: $black1;
      display: block;
      font-size: 20px;
      padding-bottom: 15px;
      @include semibold();
    }

    .op-heading {
      color: $black1;
      font-size: 20px;
      margin-bottom: 10px;
      @include semibold();
    }

    .op-flex-container {
      padding: 2px 0;
      @include flexbox($wrap: nowrap);

      .flex-item-1 {
        width: 60%;
        text-align: left;
      }

      .flex-item-2 {
        width: 40%;
        text-align: right;
      }
    }

    .op-label,
    .op-amount {
      font-size: 14px;
      color: $grey;
      display: block;
      @include medium();

      &.green {
        color: #1daf3a;
      }

      &.big {
        font-size: 16px;
        color: $black1;
        margin-top: 5px;
        @include semibold();
      }
    }
  }

  .action-btn-bottom-fixed-box {
    left: 0;
    right: 0;
    bottom: 0;
    padding: 15px 0;
    position: absolute;
    background: $white;
    width: $drawerWidth;
    z-index: $zCartPlaceOrderBtn;
    box-shadow: 0px -3px 6px #0000000a;
  }

  .checkout-action-btn-box {
    display: flex;
    padding: 0 16px;
    justify-content: space-between;

    .item-1 {
      width: 35%;
      min-width: 115px;
    }

    .item-2 {
      width: 60%;
    }

    .c-price {
      color: $black1;
      font-size: 20px;
      display: block;
      @include medium();
    }

    .c-view-detail-btn {
      @include btnReset();
      font-size: 12px;
      color: #8e1824;
      @include semibold();
      background-color: transparent;
    }

    .action-btn {
      margin: 0;
    }
  }
}

.mobile-register-login-page-container {
  margin: 0 auto;
  padding-bottom: 150px;

  .pd-horizontal {
    padding: 0 4px;
  }

  .heading {
    color: $black;
    font-size: 28px;
    padding: 20px 0 30px;
    @include semibold();
  }

  .input-flex-container {
    width: 100%;
    padding: 25px 0;
    @include flexbox($align: normal, $wrap: nowrap, $justify: normal);

    .i-flex-item-1 {
      width: 35%;
    }

    .i-flex-item-2 {
      width: 65%;
    }

    &.isError {
      .i-label {
        color: $lightRed;
      }

      .i-input-control {
        color: $lightRed;
        border-bottom: 2px solid $lightRed;

        &:focus {
          border-bottom: 2px solid $lightRed;
        }
      }
    }
  }

  .m-bottom-fixed {
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    padding: 15px;
    margin: 0 auto;
    position: fixed;
    max-width: 1000px;
    background: $white;
    padding-bottom: 15px;
    z-index: $zIdxModalBottomBtn;
    box-shadow: 0px -3px 6px #0000000a;
  }

  .brd-top {
    border-top: 8px solid #f0f2f9;
  }

  .action-btn {
    @include semibold();
    width: 100%;
  }

  .checkbox-item {
    padding: 10px 0 20px;
  }

  .pd-vertical {
    padding: 30px 0 60px;
  }

  .sub-txt {
    color: $grey;
    display: block;
    font-size: 14px;
    text-align: center;
    @include medium();

    &:nth-of-type(1) {
      padding-top: 20px;
    }
  }

  .social-btn-wrap {
    margin: 15px auto 20px auto;
    @include flexbox();

    .social-btn {
      @include btnReset();
      background-color: transparent;
    }
  }

  .already-acct-txt {
    color: $grey;
    display: block;
    font-size: 16px;
    text-align: center;
    @include medium();
  }

  .login-txt {
    color: $black1;
    font-size: 14px;
    cursor: pointer;
    margin-left: 6px;
    @include semibold();
    text-decoration: underline;
  }

  .forgot-pass-txt {
    color: $black1;
    font-size: 14px;
    cursor: pointer;
    margin-left: 6px;
    display: block;
    text-align: right;
    text-transform: uppercase;
    @include semibold();
  }
}

.mobile-register-personal-details-page-container {
  margin: 0 auto;
  max-width: 1000px;
  padding: 0 4px 150px 4px;

  .heading {
    color: $black;
    font-size: 28px;
    padding: 20px 0 30px;
    @include semibold();
  }

  .input-flex-container {
    width: 100%;
    padding: 25px 0;
    @include flexbox($align: normal, $wrap: nowrap, $justify: normal);

    .i-flex-item-1 {
      width: 35%;
    }

    .i-flex-item-2 {
      width: 65%;
    }

    &.isError {
      .i-label {
        color: $lightRed;
      }

      .i-input-control {
        color: $lightRed;
        border-bottom: 2px solid $lightRed;

        &:focus {
          border-bottom: 2px solid $lightRed;
        }
      }
    }
  }

  .image-preview-wrap {
    position: relative;
    display: inline-block;

    .camera-icon-wrap {
      right: 0;
      bottom: 0;
      position: absolute;
    }

    .imageWrapper {
      top: 0;
      left: 0;
      width: 74px;
      height: 74px;
      cursor: pointer;
      overflow: hidden;
      border-radius: 50%;
      position: absolute;
    }

    .user-img {
      @include fitCover();
    }

    .file-upload {
      margin: 0;
      padding: 0;
      width: 74px;
      height: 74px;
      border: none;
      cursor: pointer;
      overflow: hidden;
      position: relative;
      color: transparent;
      display: inline-block;
      background: transparent;
    }

    .file-upload input.file-input {
      top: 0;
      right: 0;
      margin: 0;
      padding: 0;
      opacity: 0;
      width: 100%;
      height: 100%;
      font-size: 20px;
      cursor: pointer;
      position: absolute;
      filter: alpha(opacity=0);
    }
  }

  .radio-flex-container {
    @include flexbox($align: normal, $justify: normal);
  }

  .i-radio-btn-wrap {
    width: 39px;
    height: 39px;
    border-radius: 50%;
    position: relative;
    margin-right: 20px;
    background: transparent;
    @include flexbox($justify: center);
    cursor: pointer;

    &.genderChecked {
      background-color: $black1;

      svg path {
        fill: $white;
      }
    }

    .i-radio-btn {
      left: 0;
      opacity: 0;
      width: 100%;
      height: 100%;
      cursor: pointer;
      position: absolute;
    }
  }

  .m-bottom-fixed {
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    padding: 15px;
    margin: 0 auto;
    position: fixed;
    max-width: 1000px;
    background: $white;
    padding-bottom: 15px;
    z-index: $zIdxModalBottomBtn;
    box-shadow: 0px -3px 6px #0000000a;
  }

  .action-btn {
    @include semibold();
  }
}

.mobile-register-address-details-page-container {
  margin: 0 auto;
  max-width: 1000px;
  padding: 0 4px 150px 4px;

  .heading {
    color: $black;
    font-size: 28px;
    padding: 20px 0 30px;
    @include semibold();
  }

  .address-item-wrap {
    padding: 24px;
    margin-top: 40px;
    border-radius: 8px;
    margin-bottom: 24px;

    &.new-address-wrap {
      background: #f9f9f9;
      box-shadow: -2px 3px 6px #0000000f;
      background-image: url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' rx='10' ry='10' stroke='%23333' stroke-width='3' stroke-dasharray='7' stroke-dashoffset='0' stroke-linecap='butt'/%3e%3c/svg%3e");

      .flex-container {
        text-align: center;
        align-items: center;
        justify-content: center;
      }

      .a-new {
        display: block;
        color: $black1;
        font-size: 14px;
        margin-top: 10px;
        text-transform: uppercase;
        @include medium();
      }
    }
  }

  .m-bottom-fixed {
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    padding: 15px;
    margin: 0 auto;
    position: fixed;
    max-width: 1000px;
    background: $white;
    padding-bottom: 15px;
    z-index: $zIdxModalBottomBtn;
    box-shadow: 0px -3px 6px #0000000a;
  }

  .action-btn {
    @include semibold();
  }
}

.no-design-box {
  height: 500px;
  display: flex;
  align-items: center;
  justify-content: center;

  .msg {
    color: $black1;
    font-size: 27px;
    @include medium();
  }
}

@media only screen and (min-width: 1000px) {
  .d-no-padding {
    padding: 0;
  }

  .d-no-margin {
    margin: 0;
  }

  .d-width-30 {
    width: 30% !important;
  }

  .d-width-70 {
    width: 70% !important;
  }
}

.zoomImgWrap {
  height: 100%;
  display: block;
  cursor: pointer;
  position: relative;

  &:after {
    content: '';
    top: 0;
    right: 0;
    width: 33px;
    height: 33px;
    display: block;
    position: absolute;
  }

  img {
    display: block;

    &::selection {
      background-color: transparent;
    }
  }
}

@media only screen and (max-width: 999px) {
  body {
    padding-top: 60px;
    background-color: $white;
  }

  .horizontal-padding {
    padding: 0;
  }

  .d-view {
    display: none !important;
  }

  .m-view {
    display: block !important;
  }

  .xs-no-padding {
    padding: 0;
  }

  .xs-no-margin {
    margin: 0;
  }

  .no-padding-horizontal {
    padding-right: calc(var(--bs-gutter-x) * 1.1);
    padding-left: calc(var(--bs-gutter-x) * 0.1);

    &:nth-child(odd) {
      padding-left: 0;
    }
  }
}

.dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-content {
  right: -72px;
  display: none;
  position: absolute;
  background-color: #f1f1f1;
  min-width: 160px;
  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
  z-index: 1;
}

.drpdown-div {
  right: 23px;
}

.dropdown-content a {
  color: black;
  padding: 12px 16px;
  text-decoration: none;
  display: block;
}

.dropdown-content a:hover {
  background-color: #ddd;
}

.dropdown:hover .dropdown-content {
  display: block;
}

.celebrity-picks-wrapper {
  background-color: #fff;
  background-repeat: repeat;
  background-size: contain;
  position: relative;
  min-height: 45rem;
  z-index: 1;
  margin-top: 3rem;
  margin-bottom: 6rem;

  .heading-box {
    display: inline-block;
    padding-top: 250px;
    text-align: center;
  }

  .slider-box {
    // width: calc(100% - (((100% - 1110px) / 2) + 19.4%));
    width: calc(100vw - 35vw);
    overflow: hidden;
    position: absolute;
    right: 0;
  }

  .heading {
    color: $white;
    font-size: 56px;
    max-width: 280px;
    text-align: center;
    line-height: 80px;
    @include medium();
  }

  .slider-slide-btn {
    @include btnReset();
    margin-top: 0px;
    background-color: transparent;
  }

  .cp-carousel-item {
    .cp-img-box {
      width: 344px;
      height: 456px;
    }

    .cp-card-img {
      @include fitCover();
    }

    .cp-card-title {
      color: $white;
      font-size: 24px;
      line-height: 30px;
      padding: 15px 0;
      @include medium();
    }
  }
}

.mirraw-picks-wrapper {
  background: #F0F2F9;
  background-repeat: repeat;
  background-size: contain;
  position: relative;
  min-height: 780px;
  margin: -25px 30px -25px 30px;

  .header-img-box {
    padding: 50px;

    .header-img {
      max-width: 360px;
      margin: 0 auto;
      display: block;
    }
  }

  .mp-swiper-slide {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .mp-carousel-item {
    width: 360px;
    text-align: center;

    .mp-img-box {
      width: 360px;
      height: 400px;
      overflow: hidden;
    }

    .mp-card-img {
      @include fitCover();
    }

    .mp-txt-box {
      padding: 20px 10px;
    }

    .mp-card-name {
      color: $white;
      font-size: 24px;
      line-height: 30px;
      padding-bottom: 10px;
      @include semibold();
    }

    .mp-card-des {
      color: #bcbcbc;
      font-size: 20px;
      line-height: 30px;
      padding-bottom: 10px;
      @include regular();
    }

    .mp-card-amount {
      color: $white;
      font-size: 32px;
      @include semibold();
    }
  }
}

@media only screen and (min-width: 1000px) and (max-width: 1281px) {
  body {
    padding-top: 109px;
  }
}

.product-detail-page-container {
  .vendor-detail-box {
    padding: 12px;
    margin-top: 10px;
    position: relative;
    border-radius: 12px;
    background-size: cover;
    background-repeat: no-repeat;
    background-image: image-url('pd_vendor_bg.png');
    @include flexbox($wrap: nowrap, $justify: normal);

    .logo-box {
      width: 60px;
      height: 60px;
      background-color: $white;
      border-radius: 12px;
      overflow: hidden;
    }

    .v-name {
      color: $white;
      font-size: 20px;
      @include semibold();
    }

    .item-count {
      display: block;
      font-size: 14px;
      color: #ffffff99;
      @include regular();
    }

    .v-info {
      margin-left: 15px;
    }

    .right-caret-icon {
      right: 20px;
      position: absolute;
    }
  }
}

#seo_post {
  padding: 1rem;
}

#seo_post h2 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.3rem;
}

#seo_post h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.3rem;
}

#seo_post h4 {
  font-size: 1rem;
  margin-bottom: 0.3rem;
  margin-top: 0.9rem;
  font-weight: 600;
}

#seo_post p {
  font-size: 0.875rem;
  text-align: justify;
  margin-bottom: 0.9rem;
  line-height: 1.6;
}

#seo_post p a {
  font-weight: bold;
  color: #b01f2b;
  font-size: 14px;
}

#seo_post ul {
  font-size: 0.875rem;
  text-align: justify;
  margin-left: 1.1rem;
}

#seo_post ul li {
  margin-bottom: 0.2rem;
}

.bcrumb {
  font-size: 12px !important;
  color: #00000099 !important;
}

.footer-info #popular_search {
  font-size: 14px !important;
  color: #00000099 important;
}

.footer-info #popular_search a {
  font-size: 14px !important;
  color: #00000099 important;
}

#seo_post a {
  font-size: 0.875em;
}

#seo_post ol {
  margin-left: 1.1rem;
}

#seo_post strong {
  font-size: 14px;
}

#seo_post li {
  font-size: 14px;
}

#read-less {
  display: none;
}

.read_more {
  margin-left: 1.1rem;
  margin-top: -1.3rem;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  color: #f56a6a;
  display: none;
}

.read_less {
  font-size: 0.875rem;
  margin-left: 1.1rem;
  margin-top: -0.5rem;
  font-weight: 600;
  display: none;
  cursor: pointer;
  color: #f56a6a;
}

#read-less1 {
  display: none;
}

#read-more1 {
  line-height: 1.6;
  font-size: 0.875rem;
}

#read-less1 {
  line-height: 1.6;
  font-size: 0.875rem;
}

.read_more1 {
  line-height: 1.6;
  font-size: 0.875rem;
  cursor: pointer;
}

.read_less1 {
  line-height: 1.6;
  font-size: 0.875rem;
  cursor: pointer;
}

#seo_content {
  margin-left: 15px;
}

.common-product-card-wrapper .p-img-box .wishlist-remove-btn {
  position: absolute;
  color: #777;
  right: 10px;
  top: 10px;
  border-radius: 50%;
  border: 1px solid #777;
  font-size: 13px;
}

@media screen and (max-width: 575px) {
  .product-list-wrapper {
    .no-padding-horizontal {
      padding-right: calc(var(--bs-gutter-x) * 0.1);
      padding-left: calc(var(--bs-gutter-x) * 0.1);

      &:nth-child(even) {
        padding-right: 0;
      }

      &:nth-child(odd) {
        padding-left: 0;
      }
    }
  }
}
