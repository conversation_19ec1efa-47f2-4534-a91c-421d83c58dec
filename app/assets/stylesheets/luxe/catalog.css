.catalog-page-container {
  padding-bottom: 40px;
}
.catalog-page-container .main-flex-container {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  -webkit-box-pack: normal;
  -ms-flex-pack: normal;
  justify-content: normal;
}
.catalog-page-container .main-flex-container .flex--item-1 {
  max-width: 275px;
  padding: 15px 26px;
  background: #ffffff;
  border-right: 2px solid #f0f2f9;
  height: 75vh;
  overflow-y: auto;
}
.catalog-page-container .main-flex-container .flex--item-1::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}
.catalog-page-container
  .main-flex-container
  .flex--item-1::-webkit-scrollbar-track {
  background: transparent;
}
.catalog-page-container
  .main-flex-container
  .flex--item-1::-webkit-scrollbar-thumb {
  background-color: #00443d;
  border-radius: 20px;
  border: transparent;
}
.catalog-page-container .main-flex-container .sticky-element {
  top: 142px;
  position: sticky;
}
.catalog-page-container .main-flex-container .flex--item-2 {
  flex: 1;
  padding: 30px;
  max-width: 915px;
}
.catalog-page-container .ready-ship-wrapper {
  padding: 15px 0;
  border-bottom: 1px solid #0000000f;
}
.catalog-page-container .breadcrumb-wrapper .b-item {
  display: inline;
}
.catalog-page-container .breadcrumb-wrapper .b-item:last-child .b-link {
  color: #000000cf;
}
.catalog-page-container .breadcrumb-wrapper .b-item + .b-item:before {
  padding: 8px;
  color: #00000066;
  content: "/\00a0";
}
.catalog-page-container .breadcrumb-wrapper .b-link {
  color: #00000066;
  font-size: 14px;
  text-decoration: none;
  text-transform: capitalize;

  font-weight: normal;
}
.catalog-page-container .breadcrumb-wrapper .b-link:hover {
  color: #000000;
}
.catalog-page-container .filter-accordion-wrapper .accordion-item {
  border: none;
  min-width: 215px;
  border-bottom: 1px solid #0000000f;
}
.catalog-page-container .filter-accordion-wrapper .accordion-item:last-child {
  border-bottom: none;
}
.catalog-page-container .filter-accordion-wrapper .accordion-button::after {
  zoom: 1.3;
}
.catalog-page-container .filter-accordion-wrapper .accordion-body {
  max-height: 200px;
  padding: 0;
}

.catalog-page-container
  .filter-accordion-wrapper
  .accordion-body
  .label-custom {
  font-size: 14px;
}

.catalog-page-container
  .filter-accordion-wrapper
  .accordion-body
  .facet-link-desktop {
  display: flex;
  gap: 8px;
  align-items: start;
}

.catalog-page-container
  .filter-accordion-wrapper
  .accordion-body
  .on-off-checkbox {
  display: grid;
  align-self: center;
}

.catalog-page-container .filter-accordion-wrapper .filter-header-title {
  background: #ffffff;
  border: none;
  box-shadow: none;
  border-radius: 0;
  padding: 10px 0;

  font-weight: 600;
  color: #000000cc;
  text-transform: capitalize;
}
.catalog-page-container .filter-accordion-wrapper .active-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #1daf3a;
  margin-top: -5px;
  margin-left: 5px;
}
.catalog-page-container .filter-accordion-wrapper .clear-btn {
  background: #fff3f3;
  border-radius: 7px;
  color: #f56a6a;

  font-weight: 500;
  font-size: 10px;
  text-transform: uppercase;
  border: 1px solid #f56a6a;
  padding: 5px 7px;
  position: absolute;
  right: 60px;
}
.catalog-page-container .filter-accordion-wrapper .checkbox-list,
.catalog-page-container .filter-accordion-wrapper .radio-list {
  margin: 24px 0;
  max-height: 385px;
  overflow-y: auto;
}
.catalog-page-container .filter-accordion-wrapper .radio-list {
  margin: 5px 0;
}
.catalog-page-container .filter-accordion-wrapper .checkbox-item,
.catalog-page-container .filter-accordion-wrapper .radio-item {
  margin: 16px 0;
}
.catalog-page-container .filter-accordion-wrapper .checkbox-item:first-child,
.catalog-page-container .filter-accordion-wrapper .radio-item:first-child {
  margin: 0;
}
.catalog-page-container .filter-accordion-wrapper .price-range-wrap {
  margin-bottom: 20px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  -webkit-box-align: normal;
  -ms-flex-align: normal;
  align-items: normal;
  -webkit-box-pack: normal;
  -ms-flex-pack: normal;
  justify-content: normal;
}
.catalog-page-container .filter-accordion-wrapper .price-range-wrap .max-box {
  margin-left: 15px;
}
.catalog-page-container .heading-sort-wrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: normal;
  -ms-flex-align: normal;
  align-items: normal;
  -webkit-box-pack: space-between;
  -ms-flex-pack: space-between;
  justify-content: space-between;
}
.catalog-page-container .heading-sort-wrapper .category-heading {
  display: inline-block;
  font-family: "Cormorant Garamond" !important;
  font-weight: 600;
  font-size: 24px;
  color: #000000;
}
.catalog-page-container .heading-sort-wrapper .category-heading::first-letter {
  text-transform: uppercase !important;
}
.catalog-page-container .heading-sort-wrapper .item-count {
  font-weight: normal;
  color: #636466;
  margin-left: 5px;
}
.catalog-page-container .heading-sort-wrapper .heading-wrap {
  width: 50%;
  margin-bottom: 14px;
}
.catalog-page-container .heading-sort-wrapper .sort-by-wrap {
  margin-right: 40px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: flex-start;
  -ms-flex-pack: flex-start;
  justify-content: flex-start;
}
.catalog-page-container .heading-sort-wrapper .sort-by-wrap .sort-by-txt {
  color: #767779;
  text-transform: uppercase;

  font-weight: 500;
}
.catalog-page-container
  .heading-sort-wrapper
  .sort-by-wrap
  .sort-by-dropdown-btn {
  background-color: transparent;
  color: #1f1f1f;
  font-size: 14px;
  border: none;
  outline: none;
  box-shadow: none;
  min-width: 180px;
  padding-right: 25px;
  text-transform: uppercase;

  font-weight: 600;
}
.catalog-page-container
  .heading-sort-wrapper
  .sort-by-wrap
  .sort-by-dropdown-btn::after {
  position: absolute;
  right: 0;
  top: 5px;
  border: none;
  content: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cg id='icon_Right_arrow_small' data-name='icon/Right arrow/small' transform='translate(24) rotate(90)'%3E%3Cpath id='Path_102951' data-name='Path 102951' d='M1063.648,1252.947a.651.651,0,0,1,.918,0l3.459,3.471h0a1.3,1.3,0,0,1,0,1.836h0l-3.489,3.478a.651.651,0,0,1-.918-.918l3.2-3.192a.4.4,0,0,0,0-.571l-3.174-3.193A.65.65,0,0,1,1063.648,1252.947Z' transform='translate(-1053.462 -1245.758)' fill='rgba(0,0,0,0.8)'/%3E%3Crect id='Rectangle_21663' data-name='Rectangle 21663' width='24' height='24' fill='none'/%3E%3C/g%3E%3C/svg%3E%0A");
}
.catalog-page-container .heading-sort-wrapper .sort-by-wrap .dropdown-item {
  cursor: pointer;
  color: #1f1f1f;
  font-size: 14px;
  text-transform: uppercase;
}
.catalog-page-container
  .heading-sort-wrapper
  .sort-by-wrap
  .dropdown-item.active,
.catalog-page-container
  .heading-sort-wrapper
  .sort-by-wrap
  .dropdown-item:active {
  background-color: #e9ecef;
}
.catalog-page-container .product-list-wrapper {
  padding-top: 30px;
}
.catalog-page-container .product-coupon-code-wrapper {
  position: relative;
}
.catalog-page-container .product-coupon-code-wrapper .img-txt-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  padding: 50px;
}
.catalog-page-container .product-coupon-code-wrapper .info-txt {
  font-weight: 900;
  text-transform: uppercase;
  font-size: 32px;
  color: #ffffff;
}
.catalog-page-container .product-coupon-code-wrapper .banner-img {
  max-width: 300px;
}
.catalog-page-container .product-coupon-code-wrapper .code-btn {
  font-size: 16px;
  color: #1f1f1f;
  text-transform: uppercase;
  display: block;

  font-weight: bold;
  border: none;
  outline: none;
  box-shadow: none;
  border-radius: 8px;
  background-color: #ffffff;
  padding: 13px 27px;
  margin-top: 20px;
  -webkit-transition: -webkit-transform 0.1s ease;
  transition: -webkit-transform 0.1s ease;
  transition: transform 0.1s ease;
  transition: transform 0.1s ease, -webkit-transform 0.1s ease;
}
.catalog-page-container .product-coupon-code-wrapper .code-btn:hover {
  -webkit-transform: translateY(-2px);
  transform: translateY(-2px);
}
@media only screen and (min-width: 769px) and (max-width: 999px) {
  .catalog-page-container .product-coupon-code-wrapper .info-txt {
    font-size: 25px;
  }
  .catalog-page-container .product-coupon-code-wrapper .code-btn {
    font-size: 14px;
    padding: 10px 25px;
    margin-top: 10px;
  }
}
@media only screen and (max-width: 768px) {
  .catalog-page-container .product-coupon-code-wrapper .img-txt-overlay {
    padding: 10px 0;
  }
  .catalog-page-container .product-coupon-code-wrapper .info-txt {
    font-size: 10px;
    line-height: 20px;
  }
  .catalog-page-container .product-coupon-code-wrapper .code-btn {
    padding: 10px;
    font-size: 10px;
    margin-top: 10px;
  }
}
.catalog-page-container .horizontal-banner-msg-wrapper {
  padding: 0px 0px 5px;
}
.catalog-page-container .horizontal-banner-msg-wrapper .bg-img {
  height: 100%;
  position: relative;
}
.catalog-page-container .horizontal-banner-msg-wrapper .msg-box {
  position: absolute;
  top: 18px;
  left: 24px;
  background-color: #fff;
  max-width: 400px;
  border-radius: 16px;
  padding: 24px;
  text-align: left;
}
.catalog-page-container .horizontal-banner-msg-wrapper .m-heading {
  font-size: 20px;
  color: #000000de;

  font-weight: 600;
}
.catalog-page-container .horizontal-banner-msg-wrapper .m-des {
  display: block;
  color: #1f1f1f;
  font-size: 14px;

  font-weight: normal;
}
.catalog-page-container .sort-filter-drawer-wrapper {
  bottom: 0;
  width: 100%;
  display: none;
  position: fixed;
  max-width: 1366px;
  z-index: 130;
  background-color: #ffffff;
  border-top: 1px solid #eaeaec;
}
.catalog-page-container .sort-filter-drawer-wrapper .flex-container {
  text-align: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  -webkit-box-pack: normal;
  -ms-flex-pack: normal;
  justify-content: normal;
}
.catalog-page-container .sort-filter-drawer-wrapper .flex-container .flex-item {
  width: 50%;
}
.catalog-page-container .sort-filter-drawer-wrapper .divider {
  position: relative;
}
.catalog-page-container .sort-filter-drawer-wrapper .divider::after {
  content: "";
  right: 0;
  top: 15px;
  width: 2px;
  height: 35px;
  position: absolute;
  background-color: #00000014;
}
.catalog-page-container .sort-filter-drawer-wrapper .action-btn {
  width: 100%;
  padding: 10px;
  display: block;
  border: none;
  outline: none;
  box-shadow: none;
  background-color: #ffffff;
}
.catalog-page-container .sort-filter-drawer-wrapper .svg--icon {
  margin-top: -5px;
}
.catalog-page-container .sort-filter-drawer-wrapper .txt-1 {
  font-size: 16px;
}
.catalog-page-container .sort-filter-drawer-wrapper .txt-2 {
  display: block;
  font-size: 10px;
  color: #7b7b7b;
}
.catalog-page-container .sort-by-popup-cover-black {
  top: 0;
  left: 0;
  height: 0;
  z-index: 140;
  opacity: 0;
  width: 100%;
  display: none;
  position: fixed;
  background-color: rgba(0, 0, 0, 0.55);
  -webkit-transition: opacity 0.5s;
  -o-transition: opacity 0.5s;
  transition: opacity 0.5s;
}
.catalog-page-container .sort-filter-option-wrapper {
  bottom: 0;
  height: 0;
  width: 100%;
  z-index: 150;
  display: none;
  position: fixed;
  background-color: #ffffff;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
}
.catalog-page-container .sort-filter-option-wrapper .radio-list {
  padding: 10px;
}
.catalog-page-container .sort-filter-option-wrapper .radio-item {
  margin: 15px 0;
}
.home-page-container .circular-categories-wrapper {
  display: none;
}
.home-page-container .circular-categories-wrapper .flex-container {
  display: flex;
  flex-wrap: nowrap;
  overflow-x: auto;
  padding: 16px;
}
.home-page-container .circular-categories-wrapper .flex-container .flex-item {
  margin-right: 16px;
  text-align: center;
  flex: 0 0 auto;
}
.home-page-container
  .circular-categories-wrapper
  .flex-container
  .flex-item:last-child {
  margin-right: 0;
}
.home-page-container .circular-categories-wrapper .a-link {
  display: block;
}
.home-page-container .circular-categories-wrapper .circular-img {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  -o-object-fit: cover;
  object-fit: cover;
  -o-object-position: top;
  object-position: top;
}
.home-page-container .circular-categories-wrapper .c-lable {
  display: block;
  font-size: 10px;
  color: #000000;
  padding-top: 5px;
  text-transform: uppercase;

  font-weight: 500;
}
@media only screen and (max-width: 999px) {
  .home-page-container .circular-categories-wrapper {
    display: block;
    margin-top: 0 !important;
  }
  .circular-categories-wrapper {
    display: none;
    .flex-container {
      display: flex;
      flex-wrap: nowrap;
      overflow-x: auto;
      padding: 16px;
      .flex-item {
        margin-right: 16px;
        text-align: center;
        flex: 0 0 auto;
        &:last-child {
          margin-right: 0;
        }
      }
    }
    .a-link {
      display: block;
    }
    .circular-img {
      width: 48px;
      height: 48px;
      border-radius: 50%;
    }
    .c-lable {
      display: block;
      font-size: 10px;
      color: black;
      padding-top: 5px;
      text-transform: uppercase;
    }
  }
  .catalog-page-container {
    background: #ffffff;
    padding-bottom: 25px;
    padding-top: 52px;
  }
  .catalog-page-container .main-flex-container .flex--item-1 {
    width: 100%;
    display: none;
  }
  .catalog-page-container .main-flex-container .flex--item-2 {
    padding: 2px 0 0 0;
    width: 100%;
  }
  .catalog-page-container .heading-sort-wrapper .heading-wrap {
    width: 100%;
  }
  .catalog-page-container .heading-sort-wrapper .category-heading {
    font-size: 21px;
  }
  .catalog-page-container .heading-sort-wrapper .item-count {
    font-size: 12px;
  }
  .catalog-page-container .heading-sort-wrapper .sort-by-wrap {
    display: none;
  }
  .catalog-page-container .product-list-wrapper {
    padding-top: 20px;
  }
  .catalog-page-container .sort-filter-drawer-wrapper,
  .catalog-page-container .sort-filter-option-wrapper,
  .catalog-page-container .sort-by-popup-cover-black {
    display: block;
  }
  .catalog-page-container .horizontal-banner-msg-wrapper {
    display: block;
  }
  .catalog-page-container .horizontal-banner-msg-wrapper .msg-box {
    position: absolute;
    top: 10px;
    left: 10px;
    background-color: #fff;
    max-width: 238px;
    border-radius: 16px;
    padding: 4px;
    text-align: left;
  }
  .catalog-page-container .horizontal-banner-msg-wrapper .m-heading {
    font-size: 10px;
    color: #000000de;
  }
  .catalog-page-container .horizontal-banner-msg-wrapper .m-des {
    display: block;
    font-size: 8px;
  }
}
select.dsort {
  min-width: 180px;
  background-color: transparent;
  border: none;
  color: #1f1f1f;
  font-size: 14px;
  border: none;
  outline: none;
  box-shadow: none;
  min-width: 180px;
  padding-right: 24px;
  text-transform: uppercase;

  font-weight: 600;
}

.sort-by-wrap #sortByDropdownMenuBox option {
  text-align: center;
}
.ready_to_ship {
  margin-top: -125px;
  margin-left: -38px;
}
@media (max-width: 767px) {
  /* Mobile view */
  .img-responsive1 {
    max-width: 75%;
    height: 60%;
    margin-top: 27px;
    margin-left: 5px;
  }
}
.ready_for_ship {
  font-size: 16px;
}
#etaRangeCheckbox {
  height: 20px;
  width: 20px;
  display: inline-block;
  vertical-align: middle;
}
label[for="etaRangeCheckbox"] {
  display: inline-block;
  vertical-align: middle;
  margin-left: 5px; /* Adjust the margin as needed */
}
#etaRangeCheckbox1 {
  color: black;
}
label[for="etaRangeCheckbox1"] {
  color: black;
}
.ready_to_ship_div {
  background-color: white;
  position: absolute;
  top: 5;
  left: 0;
  padding-right: 10px;
  width: fit-content;
  height: auto;
  margin-top: -30px;
  color: black;
  font-size: 15px;
  margin-left: 3px;
  bottom: 0;
}

@media (max-width: 767px) {
  /* Adjust the breakpoint as needed */
  .ready_to_ship_div {
    width: 45%; /* Adjust the width for mobile devices */
    height: 7%;
    font-size: 10px;
    margin-top: -19px; /* Adjust the height for mobile devices */
    margin-left: 3px;
  }
  .image_dimension {
    height: 20px;
    width: 20px;
    margin-left: -3px;
  }
}

.thunder_image {
  background: none;
  display: inline-block;
  margin-right: -7px;
  margin-top: -3px;
}
@media only screen and (max-width: 999px) {
  div#offer_message_countdown {
    background: #f56a6a;
    width: 100%;
    max-width: 161px;
    padding: 4px;
    margin: 9px auto 8px;
    border-radius: 4px;
  }

  span.deal-end-txt {
    color: #fff;
    font-size: 13px;
    padding: 0 4px;
    font-weight: 400;
    font-weight: 600;
  }
  span#offer_message_clock {
    background: #fff;
    padding: 5px;
    font-weight: 600;
  }
}

.sort-by-wrap {
  margin-top: 10px;
  display: flex;
  justify-content: right;
}

@media screen and (max-width: 999px) {
  .sort-by-wrap {
    display: none;
  }
}

.catalog-page-container .container-layout-max {
  max-width: 100% !important;
}
.no-padding-horizontal .common-product-card-wrapper {
  width: 100%;
  height: 100%;
}
.container-layout-max .plp-page-luxe {
  margin: 0 auto;
  max-width: 1920px;
}
.catalog-page-container .main-flex-container .flex--item-11 {
  width: fit-content;
}
.catalog-page-container .main-flex-container .flex--item-22 {
  max-width: 1600px;
}
.product-list-wrapper
  .container-fluid
  .common-product-card-wrapper
  .plp-img-box {
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  height: auto;

  picture {
    width: 100%;
  }
}

@media only screen and (min-width: 714px) and (max-width: 999px) {
  .home-page-container .circular-categories-wrapper .circular-img {
    width: 85px;
    height: 85px;
  }
}

.catalog-page-container
  .sort-filter-drawer-wrapper
  .flex-container
  .flex-filter {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}
