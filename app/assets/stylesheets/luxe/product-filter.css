.product-filter-component-container {
  top: 58px;
  width: 100%;
  height: 100%;
  display: none;
  overflow: auto;
  position: fixed;
  background-color: #ffffff;
  z-index: 160;
}
.product-filter-component-container
  .checkbox-pills-content-wrapper
  .f-nav-pills {
  top: 59px;
  height: 100%;
  /*  position: fixed;*/
  min-width: 130px;
  min-height: 800px;
  background-color: #f0f2f9;
}
.product-filter-component-container
  .checkbox-pills-content-wrapper
  .f-pill-btn {
  color: #000 !important;
  border-radius: 0;
  text-align: left;
  position: relative;
  padding: 15px 10px;
  background-color: #f0f2f9;
  border-bottom: 1px solid #eaecf3;
}
.product-filter-component-container
  .checkbox-pills-content-wrapper
  .f-pill-btn::first-letter {
  text-transform: uppercase !important;
}
.product-filter-component-container
  .checkbox-pills-content-wrapper
  .f-pill-btn.active {
  background-color: #ffffff;
}
.product-filter-component-container
  .checkbox-pills-content-wrapper
  .f-pill-btn.active::after {
  content: "";
  left: 0;
  top: 0;
  width: 5px;
  height: 55px;
  position: absolute;
  background-color: #1f1f1f;
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
}
.product-filter-component-container
  .checkbox-pills-content-wrapper
  .f-tab-content {
  width: 100%;
  /*  margin-left: 130px;*/
  padding: 20px 20px 150px 20px;
  min-height: calc(100vh - 60px);
}
.product-filter-component-container
  .checkbox-pills-content-wrapper
  .checkbox-item {
  margin: 16px 0;
}
.product-filter-component-container
  .checkbox-pills-content-wrapper
  .checkbox-item:first-child {
  margin: 0;
}
.product-filter-component-container .clear-apply-btn-bottom-wrapper {
  bottom: 0;
  width: 100%;
  position: fixed;
  max-width: 1366px;
  background-color: #ffffff;
  border-top: 1px solid #eaeaec;
}
.product-filter-component-container
  .clear-apply-btn-bottom-wrapper
  .flex-container {
  padding: 15px;
  text-align: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  -webkit-box-pack: space-between;
  -ms-flex-pack: space-between;
  justify-content: space-between;
}
.product-filter-component-container
  .clear-apply-btn-bottom-wrapper
  .flex-container
  .flex-item {
  width: 48%;
}
.product-filter-component-container
  .clear-apply-btn-bottom-wrapper
  .common-white-btn {
  width: 100%;
  padding: 10px;
  display: block;
  color: #1f1f1f;
  border-radius: 8px;
  border: none;
  outline: none;
  box-shadow: none;
  text-transform: uppercase;
  border: 1px solid #1f1f1f;

  font-weight: bold;
  background-color: #ffffff;
}
.product-filter-component-container
  .clear-apply-btn-bottom-wrapper
  .common-black-btn {
  width: 100%;
  padding: 10px;
  display: block;
  color: #ffffff;
  border-radius: 8px;
  border: none;
  outline: none;
  box-shadow: none;
  text-transform: uppercase;
  border: 1px solid #1f1f1f;

  font-weight: bold;
  background-color: #1f1f1f;
}
.product-filter-component-container .mb-10 {
  margin-bottom: 10px;
}
.product-filter-component-container
  .radio-item:first-child
  .common-radio-btn-grp {
  margin-top: 0;
}
.facet-link .on-off-checkbox {
  display: inline-block !important;
}
.facet-link .on-off-radiobox {
  display: inline-block !important;
}
.facet-link-desktop .on-off-checkbox {
  display: inline-block;
}
.facet-link-desktop .on-off-radiobox {
  display: inline-block;
}

