var PAYPAL_SDK = PAYPAL_SDK || {};

PAYPAL_SDK = (function(window, document, Paypal) {
  Paypal.fundingSource = null;
  Paypal.rendered = false; // Flag to track whether the PayPal button is rendered

  Paypal.methods = {
    paypalButtonRender: function() {
      if (Paypal.rendered) return; // Prevent multiple renders
      Paypal.rendered = true;

      paypal.Buttons({
        style: {
          size: 'medium',
          color: 'gold',
          shape: 'rect',
          label: 'pay',
          tagline: false
        },

        onInit: function(data, actions) {
          PAYPAL_SDK.methods.paypalButtonHandler(actions);
          ACCORDIAN.methods.activateAccordion(".pay_online");
        },

        onClick: function(data) {
          Paypal.fundingSource = data.fundingSource;
        },

        createOrder: function() {
          // Set up a URL on your server to create the payment
          var CREATE_URL = '/orders';
          var payload = INT_PAYMENT_OPTIONS.methods.getPaymentPayload();
          payload['order[pay_type]'] = Paypal.fundingSource;
          payload['order[attempted_payment_gateway]'] = 'paypal_smartpay';
          return INT_PAYMENT_OPTIONS.methods.postApiCall(CREATE_URL, payload)
            .then(function(res) {
              if (res.has_error != undefined) {
                window.location.href = "/order/paypal_response_handling?error=true&messages=" + res.errors;
              } else if (res.redirect_to) {
                return window.location.href = res.redirect_to;
              } else {
                sessionStorage.setItem("returnUrl", res.return_url);
                return res.id;
              }
            });
        },

        onApprove: function(data, actions) {
          var EXECUTE_URL = '/order/paypal_execute';
          var data = {
            orderID: data.orderID
          };
          return fetch(EXECUTE_URL, {
            method: 'post',
            headers: {
              'content-type': 'application/json'
            },
            body: JSON.stringify(data)
          })
            .then(function(res) {
              if (res.status == 200) {
                return res.json();
              } else {
                window.location.href = '/order/paypal_response_handling?error=true';
              }
            })
            .then(function(res) {
              var return_url = sessionStorage.getItem("returnUrl");
              if (res.has_error) {
                window.location.href = '/order/paypal_response_handling?error=true&messages=' + res.message;
              } else {
                window.location.href = '/order/paypal_response_handling?returnUrl=' + return_url;
              }
            });
        },

        onError: function() {
          window.location.href = "/order/paypal_response_handling?error=true";
        }

      }).render('#paypal-button-container');
    },

    paypalButtonHandler: function(actions) {
      actions.enable();
    }
  };

  return Paypal;
})(this, this.document, PAYPAL_SDK);

// Render the PayPal button once on page load
function renderPaypalButton() {
  if (document.querySelector('#paypal-button-container') && !PAYPAL_SDK.rendered) {
    PAYPAL_SDK.methods.paypalButtonRender();
  }
}

// Listen for DOMContentLoaded or turbolinks:load to re-render PayPal button
document.addEventListener("DOMContentLoaded", function() {
  renderPaypalButton();
});

document.addEventListener("turbolinks:load", function() {
  renderPaypalButton();
});

// Check if the PayPal SDK is loaded and render the button
if (!window.paypal) {
  var script = document.createElement('script');
  script.src = "https://www.paypal.com/sdk/js?client-id=" + PAYPAL_CLIENT_ID + "&currency=" + currency_symbol;
  document.head.appendChild(script);
  script.onload = function() {
    renderPaypalButton();
  };
} else {
  renderPaypalButton();
}
