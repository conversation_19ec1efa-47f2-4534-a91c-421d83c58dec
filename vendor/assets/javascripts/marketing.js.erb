applyRating = function(){
  $('#form-rating-star').raty({
    score: function() {return $(this).data('star');},
    starOff: "<%= asset_path('star-off.jpg') %>",
    starOn: "<%= asset_path('star-on.jpg') %>",
    starHalf: "<%= asset_path('star-half.jpg') %>",
    click: function() {return $('#save-alert-message').data('rating-given',0);},
    path: '',
    showHalf: true
  });

  $('.star').raty({
    readOnly: true, score: function() {return $(this).attr('dscore');},
    starOff: "<%= asset_path('star-off.jpg') %>",
    starOn: "<%= asset_path('star-on.jpg') %>",
    starHalf: "<%= asset_path('star-half.jpg') %>",
    path: ''
  });
}

loadJqueryRaty = function(){
  loadScript("<%= asset_url('jquery.raty.js') %>", function(){
    applyRating();
  });
}

function getUrlParams(link){
  var hash, hashes, i, query;
  hash = {};
  hashes = link.split('&');
  i = 0;
  while (i < hashes.length) {
    query = hashes[i].split('=');
    hash[query[0]] = query[1];
    i++;
  }
  return hash;
};

applyCTimer = function(){
  $('[data-countdown]').each(function() {
    var end_date;
    end_date = new Date($(this).data('countdown') + ' UTC');
    $(this).slideDown().addClass('label-ribbon');
    return $(this).countdown(end_date).on('update.countdown', function(event) {
      var totalHours;
      totalHours = event.offset.totalDays * 24 + event.offset.hours;
      totalDays = parseInt(event.offset.totalDays);
      if (totalHours > 48) {
        return $(this).html(event.strftime(totalDays+1 +' Days Left'));
      } else if (totalHours <= 48 && totalHours >= 1) {
        return $(this).html(event.strftime(totalHours+1 + ' Hours Left'));
      } else {
       return $(this).html(event.strftime('%M:%S'));
      }
    }).on('finish.countdown', function(event) {
      return $(this).html('Deal Expired!').fadeOut(3000);
    });
  });
}

loadSTimer = function(){
  loadScript("<%= asset_url('countdown.min.js') %>", function(){
    applyCTimer();
  });
}


function applyColourFromLink( $this ){
  var bg_color;
  if ($('div.pages_home.page').length > 0){
    var link = $this.find('.active_clock').data('link');
    if(link != undefined){
      var hash_of_link = getUrlParams(link);
      $this.find('.countdown').css('color', '#'+hash_of_link.timer_txt_color);
      bg_color = '#'+hash_of_link.timer_bg_color
    }
  }
  return bg_color
}

applyCountdownTimer = function(){
  $('.countdown-timer').each(function() {
    var $this = $(this);
    var val_utc_cal = parseInt($this.find('.active_clock').val());
    var val_cal = new Date(val_utc_cal);
    var bg_color = applyColourFromLink($this);
    var x = setInterval(function() {
      $this.find('.clock').countdown(val_cal).on('update.countdown', function(event) {
        var format, totalHours;
        totalHours = event.offset.totalDays * 24 + event.offset.hours;
        format = "<span class='deal_timer' style='background:"+bg_color+"'"+">%M</span>:" + "<span class='deal_timer' style='background:"+bg_color+"'"+">%S</span>";
        if (totalHours > 99) {
          format = "<span class='deal_timer' style='width:60px;color:" +bg_color+"'"+">" + totalHours + "</span>:" + format;
        } else if (totalHours > 0 && totalHours < 10) {
          format = "<span class='deal_timer' style='background:"+bg_color+"'"+">0" + totalHours + "</span>:" + format;
        } else if (totalHours > 0) {
          format = "<span class='deal_timer'style='background:"+bg_color+"'"+">" + totalHours + "</span>:" + format;
        }
        return $(this).html(event.strftime(format));
      }).on('finish.countdown', function(event) {
        clearInterval(x);
        return $this.fadeOut(500)
      });
    }, 1000);
  });
}


loadCTimer = function(){
  loadScript("<%= asset_url('countdown.min.js') %>");
    window.setTimeout(function(){applyCountdownTimer();}, 500);
}


// http://www.optimizesmart.com/how-to-use-open-graph-protocol/
// function requestFb() {
//   var _fbq, fbds, s;
//   _fbq = window._fbq || (window._fbq = []);
//   if (!_fbq.loaded) {
//     fbds = document.createElement('script');
//     fbds.async = true;
//     fbds.src = '//connect.facebook.net/en_US/fbds.js';
//     s = document.getElementsByTagName('script')[0];
//     s.parentNode.insertBefore(fbds, s);
//     _fbq.loaded = true;
//   }
//   _fbq.push(['addPixelId', '691366507586398']);
// };

// http://www.optimizesmart.com/how-to-use-open-graph-protocol/
// function fbCoustomAudience() {
//   requestFb();
//   window._fbq = window._fbq || [];
//   window._fbq.push(['track', 'PixelInitialized', {}]);
// };

function gaOrder(ga_hash) {
  $.each( ga_hash['data'], function( index, value ) {
    gtag('ec:addProduct', value);
  });
  gtag('ec:setAction', ga_hash['action_name'], ga_hash['action_data']);
};

// https://www.inspectlet.com/docs
function inspectletJs() {
  var __ldinsp;
  __ldinsp = function() {
    var insp, x;
    insp = document.createElement('script');
    insp.type = 'text/javascript';
    insp.async = true;
    insp.id = 'inspsync';
    insp.src = ('https:' === document.location.protocol ? 'https' : 'http') + '://cdn.inspectlet.com/inspectlet.js';
    x = document.getElementsByTagName('script')[0];
    x.parentNode.insertBefore(insp, x);
  };
  if (window.attachEvent) {
    window.attachEvent('onload', __ldinsp);
  } else {
    window.addEventListener('load', __ldinsp, false);
  }
};

// https://www.inspectlet.com/docs
function pushWidInspectlet() {
  window.__insp = window.__insp || [];
  return __insp.push(['wid', 1062788080]);
};

// https://www.inspectlet.com/docs
function pushTagSessionInspectlet() {
  return __insp.push(['tagSession', 'orders_new']);
};

// http://support.perfectaudience.com/knowledgebase
function paRequest(pa_prodid) {
  var pa, s;
  window._pa = window._pa || {};
  _pa.productId = pa_prodid;
  pa = document.createElement('script');
  pa.type = 'text/javascript';
  pa.async = true;
  pa.src = ('https:' === document.location.protocol ? 'https:' : 'http:') + '//tag.perfectaudience.com/serve/5426ae17eff9a47c01000038.js';
  s = document.getElementsByTagName('script')[0];
  s.parentNode.insertBefore(pa, s);
};

// https://developers.google.com/+/web/api/javascript
function plusOne() {
  window.___gcfg = {
    lang: 'en-US',
    parsetags: 'onload'
  };
  (function() {
    var po, s;
    po = document.createElement('script');
    po.type = 'text/javascript';
    po.async = true;
    po.src = 'https://apis.google.com/js/plusone.js';
    s = document.getElementsByTagName('script')[0];
    s.parentNode.insertBefore(po, s);
  })();
};

afterWindowOrTrubolinksLoad(function() {
  var ga_event_action, product_id;
  if ($('.page.designs_show').length > 0 && $('#product_id').length > 0) {
    product_id = $('#product_id').html().split(' ').pop();
    ga_event_action = $('.add_to_buy_bow').length > 0 ? 'in_stock_product_view' : 'out_of_stock_product_view';
    <%# ga('send', 'event', 'Product View', ga_event_action, product_id, { %>
      <%# 'nonInteraction': 1 %>
    <%# }); %>
  }
});

afterWindowOrTrubolinksLoad(function() {
  if ($('div.designs_show.page, div.pages_home.page, div.store_flash_deals.page').length > 0){
    if (typeof ($(window).countdown) === 'function') {
      window.setTimeout(function(){applyCountdownTimer();}, 1000);
    } else {
      window.setTimeout(function(){loadCTimer();}, 1000);
    }
    if (typeof ($(window).raty) === 'function') {
      return applyRating();
    } else if ($('.reviews_ratings,.review-container').length > 0) {
      return loadJqueryRaty();
    }
  } else if ($('div.store_catalog_page.page').length > 0) {
    if (typeof ($(window).countdown) === 'function') {
      return applyCTimer();
    } else {
      return loadSTimer();
    }
  };
});


$(document).ready(function(){
  var trackingKey = 'utm_source'; //key of the tracking parameter
  var trackingValue = "criteo"; // value of the tracking parameter
  var cookie = "crtg_dd"; // cookie name where the dd value will be stored
  var days = 60; // cookie life time in days

  function time_format(d) {
    date = format_two_digits(d.getDate());
    month = format_two_digits(d.getMonth()+1);
    hours = format_two_digits(d.getHours());
    minutes = format_two_digits(d.getMinutes());
    return date + '/' + month + '-' + hours + ":" + minutes;
  }

  function format_two_digits(n) {
    return n < 10 ? '0' + n : n;
  }

  applyFlashDealsTime = function(){
    $('.fd-header').each(function() {
      var $this = $(this);
      var sd = $this.attr('value');
      var d = new Date(sd);
      var formatted_time = time_format(d);
      $this.text(formatted_time);
    });
  }
  // Scan the value of the URL parameters followed by ? sign. Create/update the cookie if parameter string contains trackingKey value only
  // Set cookie value to 1 if the value of trackingKey = trackingValue, set the value to 0 if trackingKey != trackingValue
  // This function uses cr_createCookie function
  function cr_readTracking(key, value)
  {
    var query = window.location.search.substring(1);
    var vars = query.split("&");
    for (var i=0;i<vars.length;i++) {
      var pair = vars[i].split("=");
      if (pair[0] == key) {
        if (pair[1] == value) {
          cr_createCookie(cookie,1,days);
        } else {
          cr_createCookie(cookie,0,days);
        }
      }
    }
  };
   
  // Creating or updating cookie by recreating it
  function cr_createCookie(cookie, value, days) {
    var d = new Date();
    d.setTime(d.getTime() + (days*24*60*60*1000));
    var expires = "expires="+d.toUTCString();
    document.cookie = cookie + "=" + value + "; " + expires + "; path=/";
  };
  applyFlashDealsTime();
  // Need to call only once on landing as utm_source is expected only when user lands
  cr_readTracking(trackingKey, trackingValue); // call the function to create/update cookie with deduplication value
})


// Need to reprogram this function because this is passing all views at one go where as 
// it should've been passing on scroll event
gaImpressions = function() {
  var designs = $('.ga_design_container[data-ga-data][data-ga-pending]')
  designs.each(function(){
    var $design = $(this)
    var gaData = $design.data().gaData
    <%# ga('ec:addImpression', gaData) %>
    $design.removeAttr('data-ga-pending')
  })
};

function gaPageview(setPage) {
  <%# if(setPage){ %>
    <%# new_path = window.location.pathname + window.location.search; %>
    <%# search_term = getSearchTerm(); %>
    <%# if (search_term) new_path += "&search_term=" + search_term; %>
    <%# ga('set', 'page', new_path); %>
  <%# } %>
  <%# ga('send', 'pageview'); %>
};

function getSearchTerm () {
  if (isSearchPage()) {
    var find_q_regex = /.*?q=(.*?)(?:&|$)/;
    var match = find_q_regex.exec(window.location.search);
    return (match && match[1])
  }
}

function isSearchPage() {
  return window.location.pathname == "/search"
}

function getCookieValue(cname){
  var name = cname + "=";
  var ca = document.cookie.split(';');
  for(var i=0; i<ca.length; i++) {
    var c = ca[i];
    while (c.charAt(0)==' ') c = c.substring(1);
    if (c.indexOf(name) == 0) return c.substring(name.length,c.length);
  }
  return "";
};

$(document).on('click', '.ga_design_container[data-ga-data] a', function(event){
  var $this = $(this)
  var gaContainer = $($this.parents('.ga_design_container[data-ga-data]'))
  var gaData = gaContainer.data().gaData
  <%# ga('ec:addProduct', gaData) %>
  <%# ga('ec:setAction', 'click', {'list': gaData.list}) %>
  <%# ga('send', 'event', 'UX', 'click', 'Results') %>
  
  dataLayer.push({ ecommerce: null });
  dataLayer.push({
  event: "ga4_select_item",
  ecommerce: {
    country_code: gaData.country_code,
    item_list_name: gaData.main_category || "",
    item_list_id: gaData.main_category_id || "",
    items: [
      {
        item_id: gaData.item_id,
        item_name: gaData.item_name,
        price: gaData.price,
        discount: gaData.discount,
        index: gaData.index,
        item_category: gaData.item_category,
        item_category2: gaData.item_category2,
        item_category3: gaData.item_category3,
        item_category4: gaData.item_category4,
        item_category5: gaData.item_category5,
        item_variant: gaData.item_variant,
        item_brand: gaData.item_brand,
        quantity: 1,
        item_list_name: gaData.item_list_name || "",
        item_list_id: gaData.item_list_id || "",
      },
    ],
    },
  });
    
  if (typeof Unbxd !== 'undefined') {
    unbxdTrack('click', {
      'pid': gaData.id,
      'prank': gaData.position
    });
  }
})
