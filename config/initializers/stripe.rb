stripe_supported_versions = JSON.parse(ENV['STRIPE_SUPPORTED_VERSIONS']) || {}
Rails.configuration.stripe = {
  publishable_key: ENV['STRIPE_PUBLISHABLE_KEY'],
  secret_key:      <PERSON>NV['STRIPE_SECRET_KEY'],
  redirect_to_stripe: ENV['REDIRECT_TO_STRIPE'],
  stripe_supported_versions: stripe_supported_versions,
  api_version: ENV['STRIPE_API_VERSION'],
  stripe_endpoint_sceret: ENV["STRIPE_ENDPOINT_SECRET"]
}

Stripe.api_version = Rails.configuration.stripe[:api_version]
Stripe.api_key = Rails.configuration.stripe[:secret_key]
