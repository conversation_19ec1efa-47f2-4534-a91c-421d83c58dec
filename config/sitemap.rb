# Set the host name for URL creation
require 'rubygems'
require 'sitemap_generator'
require 'carrierwave'

# Set the host name for URL creation
SitemapGenerator::Sitemap.default_host = 'https://shopmuzai.com'
SitemapGenerator::Sitemap.sitemaps_host = "https://#{ENV['S3_BUCKET']}.s3.amazonaws.com"
SitemapGenerator::Sitemap.public_path = 'tmp/'
SitemapGenerator::Sitemap.sitemaps_path = 'sitemaps/luxe'
SitemapGenerator::Sitemap.adapter = SitemapGenerator::WaveAdapter.new

SitemapGenerator::Sitemap.create do
  # Main sitemap with homepage
  add '/', :changefreq => 'daily', :priority => 0.9

  # Categories sitemap
  group(filename: :category, sitemaps_path: 'sitemaps/luxe') do
    Category.find_each do |category|
      add "/#{category.name.downcase}", :priority => 0.6, :changefreq => 'weekly', :lastmod => category.updated_at
    end
  end

  # Designers sitemap
  group(filename: :designers, sitemaps_path: 'sitemaps/luxe') do
    Designer.where(designer_type: "Tier 1 Designer", state_machine: ['approved', 'review', 'vacation']).find_each do |designer|
      add "/#{designer.cached_slug}", :priority => 0.6, :changefreq => 'weekly'
    end
  end

  # Products sitemap
  group(filename: :product, sitemaps_path: 'sitemaps/luxe') do
    Designer.where(designer_type: "Tier 1 Designer", state_machine: ['approved', 'review', 'vacation']).includes(:designs).find_each do |designer|
      designer.designs.find_each do |design|
        add "/#{designer.cached_slug}/buy/#{design.title.parameterize}/#{design.id}",
            :priority => 0.6, :changefreq => 'weekly', :lastmod => design.updated_at
      end
    end
  end

  # Trends sitemap (collections from tagged designs)
  group(filename: :trends, sitemaps_path: 'sitemaps/luxe') do
    # Get unique collection tags from designs
    collection_tags = Design.joins(:taggings)
                           .joins("INNER JOIN tags ON tags.id = taggings.tag_id")
                           .where(taggings: { context: 'collections' })
                           .joins("INNER JOIN tags AS collection_tags ON collection_tags.id = taggings.tag_id")
                           .pluck('DISTINCT collection_tags.name')

    collection_tags.each do |collection_name|
      add "/trends/#{collection_name.parameterize}",
          :priority => 0.5, :changefreq => 'weekly'
    end
  end

  #SitemapGenerator::Sitemap.ping_search_engines
end
