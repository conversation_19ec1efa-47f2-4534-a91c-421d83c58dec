Rails.application.routes.draw do

  # The priority is based upon order of creation: first created -> highest priority.
  # See how all your routes lay out with "rake routes".

  # You can have the root of your site routed with "root"
  # root 'welcome#index'

  # Example of regular route:
  #   get 'products/:id' => 'catalog#view'

  # Example of named route that can be invoked with purchase_url(id: product.id)
  #   get 'products/:id/purchase' => 'catalog#purchase', as: :purchase

  # Example resource route (maps HTTP verbs to controller actions automatically):
  #   resources :products

  # Example resource route with options:
  #   resources :products do
  #     member do
  #       get 'short'
  #       post 'toggle'
  #     end
  #
  #     collection do
  #       get 'sold'
  #     end
  #   end

  # Example resource route with sub-resources:
  #   resources :products do
  #     resources :comments, :sales
  #     resource :seller
  #   end

  # Example resource route with more complex sub-resources:
  #   resources :products do
  #     resources :comments
  #     resources :sales do
  #       get 'recent', on: :collection
  #     end
  #   end

  # Example resource route with concerns:
  #   concern :toggleable do
  #     post 'toggle'
  #   end
  #   resources :posts, concerns: :toggleable
  #   resources :photos, concerns: :toggleable

  # Example resource route within a namespace:
  #   namespace :admin do
  #     # Directs /admin/products/* to Admin::ProductsController
  #     # (app/controllers/admin/products_controller.rb)
  #     resources :products
  #   end

  # Temporary Fix
  get "/islamic-clothing/kaftans/party-kaftans" => redirect("/islamic-clothing/kaftans/party-wear-kaftans")
  get "/islamic-clothing/abaya/party-abaya" => redirect("/islamic-clothing/abaya/party-wear-abaya")
  get "/islamic-clothing/tunics/party-tunics" => redirect("/islamic-clothing/tunics/party-wear-tunics")
  get "/islamic-clothing/burka/party-burka" => redirect("/islamic-clothing/burka/party-wear-burka")
  get "/men/clothing/nehru-jacket/party-nehru-jacket" => redirect("/men/clothing/nehru-jacket/party-wear-nehru-jacket")
  get "/men/clothing/kurta-pajama/party-kurta-pajama" => redirect("/men/clothing/kurta-pajama/party-wear-kurta-pajama")
  get "/kids/girls/clothing/lehenga-choli/party-lehenga-choli" => redirect("/kids/girls/clothing/lehenga-choli/party-wear-lehenga-choli")
  get "/islamic-clothing/farasha/party-farasha" => redirect("/islamic-clothing/farasha/party-wear-farasha")
  get "/women/jewellery/earrings/party-earrings" => redirect("/women/jewellery/earrings/party-wear-earrings")
  get "/men/accessories/cufflinks/party-cufflinks" => redirect("/men/accessories/cufflinks/party-wear-cufflinks")
  get "/women/jewellery/pendants/party-pendants" => redirect("/women/jewellery/pendants/party-wear-pendants")
  get "/men/clothing/kurtas/party-kurtas" => redirect("/men/clothing/kurtas/party-wear-kurtas")
  get "/women/clothing/blouses/party-blouse" => redirect("/women/clothing/blouses/party-wear-blouse")
  get "/kids/girls/clothing/sarees/party-sarees" => redirect("/kids/girls/clothing/sarees/party-wear-sarees")
  get "/women/clothing/gowns/party-gowns" => redirect("/women/clothing/gowns/party-wear-gowns")
  get "/women/clothing/kurtas-and-kurtis/party-kurtis" => redirect("/women/clothing/kurtas-and-kurtis/party-wear-kurtis")
  get "/kids/girls/clothing/kaftans/party-kaftans" => redirect("/kids/girls/clothing/kaftans/party-wear-kaftans")

  get "/kids/boys/clothing/nehru-jacket/party-nehru-jacket" => redirect("/kids/boys/clothing/nehru-jacket/party-wear-nehru-jacket")
  get "/kids/boys/clothing/dhoti-kurta/party-dhoti-kurta" => redirect("/kids/boys/clothing/dhoti-kurta/party-wear-dhoti-kurta")
  get "/kids/boys/clothing/sherwani/party-sherwani" => redirect("/kids/boys/clothing/sherwani/party-wear-sherwani")
  get "/kids/boys/clothing/indo-western-dress/party-indo-western-dress" => redirect("/kids/boys/clothing/indo-western-dress/party-wear-indo-western-dress")
  get "/kids/boys/clothing/kurta-pyjama/party-kurta-pyjama", to: redirect(path: "/kids/boys/clothing/kurta-pyjama/party-wear-kurta-pyjama")

  get "/kids/girls/clothing/frocks/party-frocks" => redirect("/kids/girls/clothing/frocks/party-wear-frocks")
  get "/kids/girls/clothing/salwar-suits/party-salwar-suits" => redirect("/kids/girls/clothing/salwar-suits/party-wear-salwar-suits")
  get "/kids/girls/clothing/gowns/party-gowns" => redirect("/kids/girls/clothing/gowns/party-wear-gowns")

  # -----Begin Luxe Redirect Routes
  get "/Bracelets" => redirect("/bracelets")
  get "/cart.json" => redirect("/cart")
  # -----end
  post 'accounts/send_otp' => 'accounts#send_otp'
  get '/application/cache_clear' => 'application#cache_clear'

  get "/sitemaps/luxe/sitemap.xml.gz" => redirect("https://#{ENV['S3_BUCKET']}.s3.amazonaws.com/sitemaps/luxe/sitemap.xml.gz")
  get "/sitemaps/luxe/sitemap1.xml.gz" => redirect("https://#{ENV['S3_BUCKET']}.s3.amazonaws.com/sitemaps/luxe/sitemap1.xml.gz")
  get "/sitemaps/luxe/category.xml.gz" => redirect("https://#{ENV['S3_BUCKET']}.s3.amazonaws.com/sitemaps/luxe/category.xml.gz")
  get "/sitemaps/luxe/designers.xml.gz" => redirect("https://#{ENV['S3_BUCKET']}.s3.amazonaws.com/sitemaps/luxe/designers.xml.gz")
  get "/sitemaps/luxe/product.xml.gz" => redirect("https://#{ENV['S3_BUCKET']}.s3.amazonaws.com/sitemaps/luxe/product.xml.gz")
  get "/sitemaps/luxe/trends.xml.gz" => redirect("https://#{ENV['S3_BUCKET']}.s3.amazonaws.com/sitemaps/luxe/trends.xml.gz")

  RedirectionRouter.load_priority_routes
  RedirectionRouter.load
  devise_for :accounts, controllers: {
    passwords: 'accounts/passwords',
    registrations: 'accounts/registrations',
    omniauth_callbacks: 'accounts/omniauth_callbacks',
    sessions: 'accounts/sessions',
    confirmations: 'accounts/confirmations'
  }

  devise_scope :account do
    get 'accounts/guest_login', to: 'accounts/sessions#guest_login'
    post 'accounts/mobile_login', to: 'accounts/sessions#mobile_login'
    post 'accounts/create_guest', to: 'accounts/registrations#create_guest'
    post 'accounts/sign_up', to: 'accounts/registrations#create'
  end

  namespace :api do
    devise_scope :account do
      post '/v1/account/refresh', to: 'v1/devise_overrides/sessions#refresh'
      get '/v1/account/:provider/callback', to: 'v1/devise_overrides/omniauth_callbacks#omniauth_success'
      post '/v1/accounts/mobile_login', to: 'v1/devise_overrides/sessions#mobile_login', as: 'verify_otp_api'
    end

    mount_devise_token_auth_for 'Account', at: '/v1/account', controllers: {
      sessions: 'api/v1/devise_overrides/sessions',
      registrations: 'api/v1/devise_overrides/registrations',
      omniauth_callbacks: 'api/v1/devise_overrides/omniauth_callbacks'
    }


    post '/v1/accounts/send_otp', to: 'v1/accounts#send_otp'
    post '/v1/accounts/verify_email_before_login', to: 'v1/accounts#verify_email_before_login'
    post '/v1/accounts/verify_only_otp', to: 'v1/accounts#verify_only_otp'
    post '/v1/accounts/update_user_profile', to: 'v1/accounts#update_user_profile'
    post '/v1/orders/payu_response', to: 'v1/orders#payu_response'
    post '/v1/orders/verify_paytm_checksum', to: 'v1/orders#verify_paytm_checksum'
    post '/v1/orders/generate_paytm_checksum', to: 'v1/orders#generate_paytm_checksum'
    post '/v1/orders/paypal_capture', to: 'v1/orders#paypal_capture'

    get '/v1/juspay/payment_options', to: 'v1/juspay#payment_options'
    get '/v1/juspay/create_order', to: 'v1/juspay#create_order'
    get '/v1/juspay/order_status', to: 'v1/juspay#order_status'
    post '/v1/juspay/callback', to: 'v1/juspay#callback'
    get '/v1/orders/juspay_response', to: 'v1/orders#juspay_response'

    post '/v1/returns/generate_otp', to: 'v1/returns#generate_otp'
    
    post '/v1/orders/stripe_checkout', to: 'v1/orders#stripe_checkout'
    post '/v1/orders/stripe_response', to: 'v1/orders#stripe_response'
    namespace :v1, defaults: { format: 'json' } do
      scope controller: :assets do
        get :menu
        get :frontpage
        get :currency
        get :design_policies
        get :banner_slider
        get :feature_products
        get :frontpage_with_banners
        get :frontpage_with_category
        get :frontpage_with_tags
        get :design_feed
        get :landing
        get :env_vars
        get :return_reason
        get :reward
        get :offers
        get :child_category_tags
        get :hot_or_not
        get :bestseller_designs
        get :dynamic_homepage
        get :newly_added_products
        get :horizontal_menu
        get :flash_deals
        get :story_collections_set
        get :tabs
        get :payment_options
        get :cart_coupons
        get :active_coupons
      end

      scope controller: :tabs do
        get :tab_boards
        get :tab_menus
      end

      post 'gokwiks/predict_rto_risk', to: 'gokwiks#predict_rto_risk', as: 'gokwiks_predict_rto_risk'
      post 'gokwiks/create_order', to: 'gokwiks#create_order', as: 'gokwiks_create_order'
      post 'gokwiks/split_order', to: 'gokwiks#split_order', as: 'split_order'
      post 'gokwiks/update_order', to: 'gokwiks#update_order', as: 'gokwiks_update_order'
      
      scope controller: :store do
        get :search
        get :search2
        get :filters
        get :facet_url
      end

      resources :addresses, only: [] do
        get :pincode_info, on: :collection
      end

      resources :story_collections, only: [:show]

      resource :tailor, only: [] do
        get :pending_products
        get :login
        get :tailor_reviews
        post :create_issue
        post :mark_not_with_me 
        get :tailoring_batch_index
        post :update_tailoring_batch
        post :tailor_next_visit
        get :tailor_next_visit
        get :delayed_products
        get :dashboard
        get :tailoring_bag_scan
        get :product_scan
        get :urgent_stitchable_products 
      end

      resource :rack_audit, only: [] do
        get :login
        get :get_audit_rack
        post :submit_product_count
        post :skip_rack_audit
        post :mark_resolved_rack_audit
        post :submit_product_scan
        post :mark_rack_empty
      end
      get 'designs/:id/design_slug', to: 'designs#slug', as: 'design_slug'
      get 'users/user_history/:email', to: 'users#user_history'

      resources :designs, only: [:show] do
        member do
          get :cod
          get :similar_designs
          get :similar_designs_unbxd
          get :unbxd_xml
          get :set_unbxd_flag
          get :wishlist
          delete :remove_wishlist
        end
        resources :reviews, only: [:index] do
          get :rating, on: :collection
        end
        collection do
          get :pdd_design
          get :preview
          get :recent_designs
          get :autosuggests
          get :weekly_hot_selling
          get :stitching_information
          get :recommended_designs
        end
      end
      resources :countries, only: [:index] do
        member do
          get :states
        end
      end
      resources :banks, only: [:index]
      resources :designers, only: [:show, :index] do
        member do
          resource :designs do
            get :reviews, on: :collection
          end
          get :all_followers
          get :offers
          get :banner_and_tags
        end
        collection do
          get :coupons
        end
      end
      resources :surveys, only: [:create] do
        collection do
          get :questions
          post :design_review
        end
      end
      resources :freshdesk_tickets do
        collection do
          post :post_freshdesk_ticket
        end
      end
      resource :user,
        except: [:new, :edit, :destroy, :create] do
        resource :cart, only: [:show] do
          member do
            post :payment_details
            get :line_item_count
            post :generate_otp
            post :verify_otp
            get :quick_cod
          end
          resources :line_item, only: [:destroy, :create, :update] do
            member do
              delete :move_to_wishlist
              put :update_through_notification
            end
            get :returnable, on: :collection
          end

          member do
            put :assign_coupon
            post :remove_coupon
          end
        end
        put :update_notification_settings
        put :cancel_cod_order
        put :update_privacy_setting
        get :notification_settings
        get :profile
        get :following_designers
        get :following_users
        get :friends
        get :wishlist_designs
        get :reviewer
        resources :wishlists, only: [:create, :destroy, :index] do
          collection do
            get :notified_wishlists
            get :likes
            post :likes, to: "wishlists#create_like"
            delete "likes/:id", to: "wishlists#destroy_like"
          end
        end
        resource :notification, only: :show
        resources :addresses, except: [:show, :new, :edit]
        resources :addresses do 
          get :get_address, on: :collection
        end
        resource :bank_detail, except: [:new, :edit]
        resources :orders, only: [:create, :index, :show] do
          post :paypal_verify
          get :paypal_success
          get :paypal_cancel
          post :braintree_checkout
          collection do
            get :confirmed
            get :canceled
            get :payment_pending
            get :braintree_client_token
          end
        end
        resources :reviews, only: [:create] do
          collection do
            get :notified_reviews
          end
        end

        resources :returns, only: [:create, :index, :update, :show] do
          collection do
            get :completed
            get :pending
          end
        end
        scope :controller => :follows do
          post :follow_designer
          delete :unfollow_designer
          post :follow_user
          delete :unfollow_user
          post :auto_follow
        end
        scope controller: :wallets do
          get :wallet
          resources :wallets, only: [] do
            collection do
              put :apply_referral
              put :apply_wallet_referral
              put :remove_wallet_referral
              put :apply_wallet_refund
              put :remove_wallet_refund
              put :apply_refund
              delete :remove_referral
              put :remove_refund
            end
          end
        end

        get 'videos/:account_id', to: 'video_listings#videos'
        post 'upload_video', to: 'video_listings#upload_video'
        delete 'video/:id', to: 'video_listings#destroy'
      end
    end
  end

  root to: 'pages#home'

  get '/api/v1/designs/:id/get_line_items_count', to: 'designs#get_line_items_count'
  get "/designers/craftsvilla-sarees" => redirect('/404')
  get "/designers/indiarush-fashion" => redirect('/404')

  post "/razorpay_submission" => "orders#razorpay_submission"
  get '/d/:id' => redirect{|params, q| d = Design.find(params[:id]); "/designers/#{d.designer.id}/designs/#{params[:id]}"}, constraints: {id: /\d+/}
  get "search/amp" => redirect('/404') #"store#catalog_page_amp"
  get "search" => "store#catalog_page"
  get "store/:kind(/:facets)/amp" => redirect('/404') # => "store#catalog_page_amp" ,as: 'store_amp_search'
  #get "store/:kind(/:facets)" => "store#catalog_page" ,as: 'store_search'
  get "collections/:collection(/:facets)/amp" => redirect('/404') #=> "store#catalog_page_amp"
  # get "collections/:collection(/:facets)" => "store#catalog_page"
  get "online/:collection(/:facets)/amp" => redirect('/404') #=> "store#catalog_page_amp"
  get "online/:collection(/:facets)" => "store#catalog_page"

  get "trends/:collection(/:facets)" => "store#catalog_page"

  get "/x-price(/:facets)" => "store#catalog_page", kind: 'direct_dollar'
  get "/api/v1/x-price(/:facets)" => "store#search", kind: 'direct_dollar'
  get "/buy-m-get-n-free(/:facets)/amp" => redirect('/404') #=> "store#catalog_page_amp", kind: 'b1g1'
  get "/buy-m-get-n-free(/:facets)" => redirect('/404')  #{}"store#catalog_page", kind: 'b1g1'

  get '/buy1get1-offers/amp' => redirect('/404') #, to: redirect(path: '/buy-m-get-n-free/amp')
  get '/buy1get1-offers', to: redirect(path: '/buy-m-get-n-free')
  get '/buy1get1-offers/:facets/amp' => redirect('/404') #, to: redirect(path: '/buy-m-get-n-free/%{facets}/amp')
  get '/buy1get1-offers/:facets', to: redirect(path: '/buy-m-get-n-free/%{facets}')

  get "/flash-deals" => "store#flash_deals"
  get "designers/:id(/:facets)/amp" => redirect('/404') # "store#catalog_page_amp"
  #get "designers/:id(/:facets)" => "store#catalog_page", as: 'designers'
  get "tags/amp" => redirect('/404') #{}"store#catalog_page_amp"
  get "tags" => "store#catalog_page"
  get '/price-match-guarantee' => 'pages#price_match_guarantee_tnc', as: 'price_match_guarantee_tnc'
  get "/offer-terms-and-conditions" => "pages#offer_tnc", as: 'offer_tnc'
  get '/coupons' => "pages#coupons"
  get '/mirraw-coupons' => "pages#mirraw_coupons"
  get "lp/:landing" => "store#landing_page", :as => "store_landing_page"
  get "onlineshop/:landing" => "pages#landing", as: "category_landing_page"
  get "order/retry/:id" => "orders#retry", :as => "order_retry"
  get "Gemstones" => 'pages#landing', landing: 'gemstones',as: 'gemstones_landing'
  get  '/designs/pdd_design'  => 'designs#pdd_design',  :as => 'api_pdd_design'
  get '/amp' => redirect('/404') #=> "pages#home_amp", as: 'root_amp'

  #direct cod confirmation not possible with cod limits, redirecting to retry page itself for cod orders in mobile
  get "order/retry_cod/:id" => "orders#retry", :as => "order_retry_cod" 
  get "order/cod_verify" => "orders#cod_verify", :as => "orders_cod_verify"
  get "order/amazon_success" => "orders#amazon_success"
  get 'orders/get_order_details' => 'orders#get_order_details'
  get 'orders/send_order_ack_email' => 'orders#send_order_ack_email'

  post 'order/paypal_execute' => 'orders#paypal_execute'
  get 'order/paypal_response_handling' => 'orders#paypal_response_handling'

  post 'carts/apply_coupon' => 'carts#apply_coupon', as: 'apply_coupon'
  post 'carts/remove_coupon' => 'carts#remove_coupon', as: 'remove_coupon'
  post 'carts/add_gift_wrap_price' => 'carts#add_gift_wrap_price'
  #otp generation and verification url for cod orders
  post 'carts/generate_otp' => 'carts#generate_otp'
  post 'carts/verify_otp' => 'carts#verify_otp'
  get 'carts/:id' => 'carts#show'

  get "user/:id/order/:order_number/return_items" => "users#return_items", as: "user_order_return_items"
  get 'user/:id/order/:order_number/cancel_cod_order' => 'users#cancel_cod_order', as: 'user_cancel_cod_order'
  post 'user/cancel_order' => 'users#cancel_order'
  get "user/:id/return_orders" => "users#return_orders", as: "return_orders"
  get "user/wallet" => "users#user_wallet", as: "user_wallet"
  get 'returns/check_tracking_number' => 'returns#check_tracking_number'
  post 'user/:id/order/:order_number/return_items' => 'returns#update_tracking_info', as: 'return_update_tracking_info'
  get 'returns/get_account_no_length' => 'returns#get_account_no_length'
  post 'returns/generate_otp' => 'returns#generate_otp'
  get '.well-known/assetlinks.json' => 'pages#assetlinks'
  post 'user/upload_full_size_photo' => 'users#upload_full_size_photo',as: 'upload_full_size_photo'
  get 'user/profile' => 'users#profile'



  resources :designers, only: [], path: '/' do
    resources :designs, only: [:show], path: '/buy/:id'
  end

  resource :cart, only: [:show] do
    put :assign_coupon
    post :save_email
  end
  resources :line_items, only: [:destroy, :create, :update]
  resource :user, only: [] do
    resources :addresses, except: [:show] do
      member do
        get :guest_edit
      end
    end
  end
  resources :accounts, only: [:update]
  resource :user, only: [] do
    resources :wishlists, only: [:new, :create, :index] do
      collection do
        delete :destroy
        get :filter
      end
    end
  end
  resources :returns, only: [:create, :update]
  resources :orders do
    collection do
      get :shipping_address
      get :billing_address
      get :create_payment
      get :display_payment_details
      get :complete_payment
      post :payu_response
      get :paypal_success
      put :paypal_retry
      put :retry_cod
      get :paypal_cancel
      post :paytm_response
      get :g2a_success
      get :stripe_response
      get :juspay_response
    end
  end

  resources :subscriptions, only: [:create]

  scope :orders do
    resources :addresses, only: [] do
      member do
        get 'edit(/:type)', to: 'addresses#edit', as: :edit_user_address
      end
      collection do
        get :billing_address, to: 'addresses#billing_address', as: 'billing_address'
        get :collect
        post :store
      end
    end
  end
  
  scope path: '/orders', controller: :orders do
    get 'pay_by_paypal/:number' => :pay_by_paypal, as: :pay_by_paypal
    get 'stripe_checkout/:number' => :redirect_for_stripe_payment, as: 'stripe_checkout'
  end
  
  resources :designs do
    collection do
      get 'more_from_category'
    end
  end
  
  resources :unbxd_recommendations, only: [] do
    collection do
      get :recommended_for_you
      get :recently_viewed
      get :more_like_these
      get :also_viewed
      get :also_bought
      get :cart_recommend
      get :top_sellers
      get :category_top_sellers
      get :brand_top_sellers
      get :pdp_top_sellers
      get :frequently_bought_together
    end
  end

  resources :stitching_measurements,only: [:index]
  post 'stitching_measurement/create' => 'stitching_measurements#create', as: 'stitching_measurements_create'
  get 'stitching_form' => 'stitching_measurements#stitching_form', as: 'stitching_form'
  get 'stitching_measurement/measurement_data' => 'stitching_measurements#measurement_data'
  get 'stitching_measurement/get_back_styles' => 'stitching_measurements#get_back_styles'
  resources :fashion_updates, only: [:show,:index], path: 'fashion-updates'
  get 'fashion-updates/category/:category', to: 'fashion_updates#category_page', as: "blog_category"
  get "/showcase/online-sales" => redirect("/online-sales")
  get "/online-sales", to: "store#dynamic_landing_page", id: "online-sales"



  get "/showcase/navratri" => redirect("/navratri-sale-offers")
  get "/navratri-sale-offers" => "store#dynamic_landing_page", id: "navratri"

  get "/showcase/diwali-sale" => redirect("/diwali-sale")
  get "/diwali-sale" => "store#dynamic_landing_page", id: "diwali-sale"

  get "/showcase/eid" => redirect("/eid") 
  get "/eid" => "store#dynamic_landing_page", id: "eid" 

  get '/showcase/:id', to: 'store#dynamic_landing_page'
  resources :horoscopes
  get 'horoscopes/daily/:id', :to => 'horoscopes#show', :as => "inside_horoscope"
  resources :surveys, only: [:new,:create]
  get '/surveys/sample_form', to: 'surveys#new', sample_form: true
  post '/surveys/update_notes', to: 'surveys#update_notes', as: 'surveys_update_notes'
  get '/surveys/get_line_item_image', to: 'surveys#get_line_item_image', as: 'get_line_item_image'
  post '/surveys/new_review', to: 'surveys#new_review', as: 'new_review'
  post '/surveys/save_review_text', to: 'surveys#save_review_text', as: 'save_review_text'
  post '/surveys/survey_answers', to: 'surveys#survey_answers'
  get '/surveys/get_audience_questions', to: 'surveys#get_audience_specific_questions', as: 'get_audience_specific_questions'
  post '/save_review', to: 'reviews#save_review'
  get 'customers/reviews', to: 'reviews#site_review', as:'site_reviews'
  get 'customers/:design_id/design_reviews', to: 'reviews#design_review', as:'design_reviews'
  post 'customers/reviews/create_review', to: 'reviews#site_review'
  post 'customers/reviews/moderate_review', to: 'reviews#site_review'
  get "user/:name" => "users#show"
  get '/country/get_states_and_dial_code' => 'addresses#get_states_and_dial_code'
  get '/country/:country/get_pincode_format' => 'addresses#get_pincode_format'
  get '/contact_us' => 'pages#contact_us'
  get '/send_feedback' => 'orders#send_feedback', as: 'send_feedback'
  get "/designers/:designer_id/designs/:design_id/similar" => "designs#similar"
  get "/designers/:designer_id/designs/:design_id/reviews" => "reviews#design_review", as: 'design_review'
  get '/designers/:designer_id/designs/:design_id/get_line_items_count', to: 'designs#get_line_items_count'
  get "/trending_searches" => 'pages#trending_searches'
  get "/tnc" => 'pages#tnc'
  get "/feedback" => 'pages#feedback'
  get 'pages/faq' => 'pages#faq'
  get 'pages/price_match' => 'pages#price_match'
  get 'pages/track' => 'pages#track'
  get 'pages/help_center' => 'pages#help_center'
  get '/apple-app-site-association' => 'pages#apple_app_site_association'
  get '/.well-known/apple-app-site-association' => 'pages#apple_app_site_association'
  post 'pages/post_freshdesk_ticket' => 'pages#post_freshdesk_ticket'
  get 'pages/stitching_information' => 'pages#stitching_information'
  get 'pages/privacy' => 'pages#privacy'
  get 'pages/terms' => 'pages#terms'
  get 'pages/prohibited' => 'pages#prohibited'
  get 'pages/about' => 'pages#about'
  get 'pages/about/amp' => redirect('/404') #'pages#about_amp'
  get "/offers" => 'pages#offers'
  get "stylist/approve_measurement" => 'stylists#approve_measurement', as: 'approve_measurement'
  match '/404' => 'pages#error_404', via: :get
  get '/bx' => 'pages#bx'
  get 'pages/bulk_order_inquiry' => 'pages#bulk_order_inquiry'
  scope path: '/sitemap', controller: :sitemaps, format: false do
    get '/' => :index, :as => :sitemaps_index
    get ':title' => :show_category, :as => :sitemaps_category
  end

  post 'gift_card_orders/paypal_response' => 'gift_card_orders#paypal_response'
  resources :gift_card_orders, only: [:new, :create, :show], param: :number

  #get 'push_engage_subscribers/create', as: :push_engage_subscribers

  namespace :one_time_passwords, path: 'otp' do
    post 'deliver'
    post 'verify'
  end
  # match 'blog' => 'wordpress#index', via: [:get, :post, :put, :patch, :delete]
  # match 'blog/*path' => 'wordpress#index', via: [:get, :post, :put, :patch, :delete]
  get '/block' => 'errors#block_page'
  get '/all-designers' => 'designers#index'

  get '/mirraw-tv' => 'video_listings#index'
  get '/mirraw-tv/:id' => 'video_listings#show', as: :video_show
  get '/sitemap.xml.gz' => 'pages#sitemapxml'
  get '/sitemap1.xml.gz' => 'pages#sitemapxml1'

  get "health" => "health#healthcheck"
  get "store/:kind(/:facets)" => "store#catalog_page" ,as: 'store_search', path: ":kind(/:facets)" 
  get "designers/:id(/:facets)" => "store#catalog_page", as: 'designers', path: ":id(/:facets)"

end
